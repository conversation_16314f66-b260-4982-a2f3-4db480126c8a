{"name": "t-chr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@clerk/elements": "^0.23.31", "@clerk/localizations": "^3.16.3", "@clerk/nextjs": "^6.20.2", "@clerk/themes": "^2.2.48", "@craftjs/core": "^0.2.7", "@hookform/resolvers": "^3.4.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toolbar": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/themes": "^3.0.3", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.0", "cmdk": "^1.0.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.31.0", "framer-motion": "^11.2.2", "lucide-react": "^0.378.0", "next": "^15.3.3", "next-intl": "^4.1.0", "pg": "^8.16.0", "react": "^18", "react-contenteditable": "^3.3.7", "react-dom": "^18", "react-hook-form": "^7.51.4", "react-player": "^2.16.0", "react-resizable-panels": "^2.0.19", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.22.1", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}