{"id": "85b94e10-6f07-466b-8aca-a983d8b8f21f", "prevId": "d4cb05b7-5fd4-492c-8834-d8e49b9143a4", "version": "7", "dialect": "postgresql", "tables": {"public.certificate_courses": {"name": "certificate_courses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "certificate_id": {"name": "certificate_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"certificate_courses_certificate_id_certificates_id_fk": {"name": "certificate_courses_certificate_id_certificates_id_fk", "tableFrom": "certificate_courses", "tableTo": "certificates", "columnsFrom": ["certificate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "certificate_courses_course_id_courses_id_fk": {"name": "certificate_courses_course_id_courses_id_fk", "tableFrom": "certificate_courses", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.certificates": {"name": "certificates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "signer_name": {"name": "signer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "signer_position": {"name": "signer_position", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "template": {"name": "template", "type": "text", "primaryKey": false, "notNull": false}, "background_image": {"name": "background_image", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"certificates_organization_id_organizations_id_fk": {"name": "certificates_organization_id_organizations_id_fk", "tableFrom": "certificates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.community_categories": {"name": "community_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"community_categories_organization_id_organizations_id_fk": {"name": "community_categories_organization_id_organizations_id_fk", "tableFrom": "community_categories", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.community_likes": {"name": "community_likes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reply_id": {"name": "reply_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"community_likes_post_id_community_posts_id_fk": {"name": "community_likes_post_id_community_posts_id_fk", "tableFrom": "community_likes", "tableTo": "community_posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "community_likes_reply_id_community_replies_id_fk": {"name": "community_likes_reply_id_community_replies_id_fk", "tableFrom": "community_likes", "tableTo": "community_replies", "columnsFrom": ["reply_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.community_posts": {"name": "community_posts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "is_pinned": {"name": "is_pinned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_locked": {"name": "is_locked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "reply_count": {"name": "reply_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_reply_at": {"name": "last_reply_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_reply_by": {"name": "last_reply_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"community_posts_organization_id_organizations_id_fk": {"name": "community_posts_organization_id_organizations_id_fk", "tableFrom": "community_posts", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "community_posts_category_id_community_categories_id_fk": {"name": "community_posts_category_id_community_categories_id_fk", "tableFrom": "community_posts", "tableTo": "community_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.community_replies": {"name": "community_replies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "post_id": {"name": "post_id", "type": "uuid", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "parent_reply_id": {"name": "parent_reply_id", "type": "uuid", "primaryKey": false, "notNull": false}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"community_replies_post_id_community_posts_id_fk": {"name": "community_replies_post_id_community_posts_id_fk", "tableFrom": "community_replies", "tableTo": "community_posts", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "community_replies_parent_reply_id_community_replies_id_fk": {"name": "community_replies_parent_reply_id_community_replies_id_fk", "tableFrom": "community_replies", "tableTo": "community_replies", "columnsFrom": ["parent_reply_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.coupon_products": {"name": "coupon_products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "coupon_id": {"name": "coupon_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"coupon_products_coupon_id_coupons_id_fk": {"name": "coupon_products_coupon_id_coupons_id_fk", "tableFrom": "coupon_products", "tableTo": "coupons", "columnsFrom": ["coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "coupon_products_product_id_products_id_fk": {"name": "coupon_products_product_id_products_id_fk", "tableFrom": "coupon_products", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.coupons": {"name": "coupons", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "discount_type": {"name": "discount_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "discount_value": {"name": "discount_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "stripe_coupon_id": {"name": "stripe_coupon_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"coupons_organization_id_organizations_id_fk": {"name": "coupons_organization_id_organizations_id_fk", "tableFrom": "coupons", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupons_code_unique": {"name": "coupons_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}}, "public.course_modules": {"name": "course_modules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "module_id": {"name": "module_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"course_modules_course_id_courses_id_fk": {"name": "course_modules_course_id_courses_id_fk", "tableFrom": "course_modules", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_modules_module_id_modules_id_fk": {"name": "course_modules_module_id_modules_id_fk", "tableFrom": "course_modules", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"courses_organization_id_organizations_id_fk": {"name": "courses_organization_id_organizations_id_fk", "tableFrom": "courses", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.landing_pages": {"name": "landing_pages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "custom_domain": {"name": "custom_domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"landing_pages_organization_id_organizations_id_fk": {"name": "landing_pages_organization_id_organizations_id_fk", "tableFrom": "landing_pages", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "landing_pages_product_id_products_id_fk": {"name": "landing_pages_product_id_products_id_fk", "tableFrom": "landing_pages", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"landing_pages_slug_unique": {"name": "landing_pages_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "public.lessons": {"name": "lessons", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "text", "primaryKey": false, "notNull": false}, "lesson_type": {"name": "lesson_type", "type": "lesson_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'video'"}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lessons_organization_id_organizations_id_fk": {"name": "lessons_organization_id_organizations_id_fk", "tableFrom": "lessons", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.module_lessons": {"name": "module_lessons", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "module_id": {"name": "module_id", "type": "uuid", "primaryKey": false, "notNull": true}, "lesson_id": {"name": "lesson_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"module_lessons_module_id_modules_id_fk": {"name": "module_lessons_module_id_modules_id_fk", "tableFrom": "module_lessons", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "module_lessons_lesson_id_lessons_id_fk": {"name": "module_lessons_lesson_id_lessons_id_fk", "tableFrom": "module_lessons", "tableTo": "lessons", "columnsFrom": ["lesson_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.modules": {"name": "modules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"modules_organization_id_organizations_id_fk": {"name": "modules_organization_id_organizations_id_fk", "tableFrom": "modules", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "clerk_org_id": {"name": "clerk_org_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "plan_type": {"name": "plan_type", "type": "plan_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'basic'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_clerk_org_id_unique": {"name": "organizations_clerk_org_id_unique", "nullsNotDistinct": false, "columns": ["clerk_org_id"]}, "organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "public.product_courses": {"name": "product_courses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"product_courses_product_id_products_id_fk": {"name": "product_courses_product_id_products_id_fk", "tableFrom": "product_courses", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_courses_course_id_courses_id_fk": {"name": "product_courses_course_id_courses_id_fk", "tableFrom": "product_courses", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_type": {"name": "payment_type", "type": "payment_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "access_duration_months": {"name": "access_duration_months", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "stripe_product_id": {"name": "stripe_product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_price_id": {"name": "stripe_price_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"products_organization_id_organizations_id_fk": {"name": "products_organization_id_organizations_id_fk", "tableFrom": "products", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.subscribers": {"name": "subscribers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "subscription_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "purchase_date": {"name": "purchase_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscribers_product_id_products_id_fk": {"name": "subscribers_product_id_products_id_fk", "tableFrom": "subscribers", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "subscribers_organization_id_organizations_id_fk": {"name": "subscribers_organization_id_organizations_id_fk", "tableFrom": "subscribers", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'editor'"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"teams_organization_id_organizations_id_fk": {"name": "teams_organization_id_organizations_id_fk", "tableFrom": "teams", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.todo": {"name": "todo", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "done": {"name": "done", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_certificates": {"name": "user_certificates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "certificate_id": {"name": "certificate_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "issued_at": {"name": "issued_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "certificate_url": {"name": "certificate_url", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_certificates_certificate_id_certificates_id_fk": {"name": "user_certificates_certificate_id_certificates_id_fk", "tableFrom": "user_certificates", "tableTo": "certificates", "columnsFrom": ["certificate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_certificates_course_id_courses_id_fk": {"name": "user_certificates_course_id_courses_id_fk", "tableFrom": "user_certificates", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_certificates_organization_id_organizations_id_fk": {"name": "user_certificates_organization_id_organizations_id_fk", "tableFrom": "user_certificates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_progress": {"name": "user_progress", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "lesson_id": {"name": "lesson_id", "type": "uuid", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "watch_time": {"name": "watch_time", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_progress_lesson_id_lessons_id_fk": {"name": "user_progress_lesson_id_lessons_id_fk", "tableFrom": "user_progress", "tableTo": "lessons", "columnsFrom": ["lesson_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_progress_course_id_courses_id_fk": {"name": "user_progress_course_id_courses_id_fk", "tableFrom": "user_progress", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_progress_organization_id_organizations_id_fk": {"name": "user_progress_organization_id_organizations_id_fk", "tableFrom": "user_progress", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"public.lesson_type": {"name": "lesson_type", "schema": "public", "values": ["video", "text", "quiz"]}, "public.payment_type": {"name": "payment_type", "schema": "public", "values": ["one_time", "monthly", "annually"]}, "public.plan_type": {"name": "plan_type", "schema": "public", "values": ["basic", "professional", "pro", "business"]}, "public.role": {"name": "role", "schema": "public", "values": ["admin", "editor"]}, "public.subscription_status": {"name": "subscription_status", "schema": "public", "values": ["active", "expired", "cancelled"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}