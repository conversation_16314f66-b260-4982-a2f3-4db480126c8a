import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

//Creates the locale routing middleware from next-intl
const intlMiddleware = createMiddleware(routing);

//Crates the public route matcher
//Any folder inside [locale] must be declared using /:locale
//The :/locale declaration servers as a catchall next-intl languages route ( br, en, etc... )

const isPublicRoute = createRouteMatcher([
  "/",
  "/:locale",
  "/:locale/sign-in(.*)",
  "/:locale/sign-up(.*)",
  "/api(.*)",
  "/images(.*)",
]);

//The Clerk middleware checks if it is a protected route or not
//It returns the login page if the page is protected and the user is not logged in
export default clerkMiddleware(
  (auth, request) => {
    if (!isPublicRoute(request)) {
      auth.protect();
    }

    //Retuns the next-intl locale from the next-intl middleware
    return intlMiddleware(request);
  },
  { debug: true }
);

//Matches all possible routes including file extensions
export const config = {
  matcher: [
    "/((?!.+\\.[\\w]+$|_next).*)",
    "/",
    "/(api|trpc)(.*)",
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
