'use server';

import { db } from '@/db';
import { organizations, teams, coupons, couponProducts, products } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createCoupon(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const code = formData.get('code') as string;
  const description = formData.get('description') as string;
  const discountType = formData.get('discountType') as 'percentage' | 'fixed';
  const discountValue = parseFloat(formData.get('discountValue') as string);
  const maxUses = formData.get('maxUses') ? parseInt(formData.get('maxUses') as string) : null;
  const validUntil = formData.get('validUntil') ? new Date(formData.get('validUntil') as string) : null;
  const productIds = formData.getAll('productIds') as string[];

  if (!code || !discountType || !discountValue) {
    throw new Error('Code, discount type, and discount value are required');
  }

  if (discountValue <= 0) {
    throw new Error('Discount value must be greater than 0');
  }

  if (discountType === 'percentage' && discountValue > 100) {
    throw new Error('Percentage discount cannot exceed 100%');
  }

  try {
    // Create coupon
    const [newCoupon] = await db
      .insert(coupons)
      .values({
        organizationId,
        code: code.toUpperCase(),
        description,
        discountType,
        discountValue: discountValue.toString(),
        maxUses,
        validUntil,
      })
      .returning();

    // Link products to coupon if provided
    if (productIds.length > 0) {
      const couponProductData = productIds.map((productId) => ({
        couponId: newCoupon.id,
        productId,
      }));

      await db.insert(couponProducts).values(couponProductData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/coupons`);
    redirect(`/${org[0].slug}/coupons`);
  } catch (error) {
    console.error('Error creating coupon:', error);
    if (error instanceof Error && error.message.includes('unique')) {
      throw new Error('Coupon code already exists');
    }
    throw new Error('Failed to create coupon');
  }
}

export async function updateCoupon(couponId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get coupon and check access
  const coupon = await db
    .select()
    .from(coupons)
    .where(eq(coupons.id, couponId))
    .limit(1);

  if (!coupon.length) {
    throw new Error('Coupon not found');
  }

  const hasAccess = await checkOrganizationAccess(coupon[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const code = formData.get('code') as string;
  const description = formData.get('description') as string;
  const discountType = formData.get('discountType') as 'percentage' | 'fixed';
  const discountValue = parseFloat(formData.get('discountValue') as string);
  const maxUses = formData.get('maxUses') ? parseInt(formData.get('maxUses') as string) : null;
  const validUntil = formData.get('validUntil') ? new Date(formData.get('validUntil') as string) : null;
  const isActive = formData.get('isActive') === 'true';
  const productIds = formData.getAll('productIds') as string[];

  if (!code || !discountType || !discountValue) {
    throw new Error('Code, discount type, and discount value are required');
  }

  if (discountValue <= 0) {
    throw new Error('Discount value must be greater than 0');
  }

  if (discountType === 'percentage' && discountValue > 100) {
    throw new Error('Percentage discount cannot exceed 100%');
  }

  try {
    // Update coupon
    await db
      .update(coupons)
      .set({
        code: code.toUpperCase(),
        description,
        discountType,
        discountValue: discountValue.toString(),
        maxUses,
        validUntil,
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(coupons.id, couponId));

    // Update coupon products
    // First, remove existing associations
    await db
      .delete(couponProducts)
      .where(eq(couponProducts.couponId, couponId));

    // Then add new associations
    if (productIds.length > 0) {
      const couponProductData = productIds.map((productId) => ({
        couponId,
        productId,
      }));

      await db.insert(couponProducts).values(couponProductData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, coupon[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/coupons`);
  } catch (error) {
    console.error('Error updating coupon:', error);
    if (error instanceof Error && error.message.includes('unique')) {
      throw new Error('Coupon code already exists');
    }
    throw new Error('Failed to update coupon');
  }
}

export async function deleteCoupon(couponId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get coupon and check access
  const coupon = await db
    .select()
    .from(coupons)
    .where(eq(coupons.id, couponId))
    .limit(1);

  if (!coupon.length) {
    throw new Error('Coupon not found');
  }

  const hasAccess = await checkOrganizationAccess(coupon[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete coupon (cascade will handle related records)
  await db
    .delete(coupons)
    .where(eq(coupons.id, couponId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, coupon[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/coupons`);
}

export async function getCoupons(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(coupons)
    .where(eq(coupons.organizationId, organizationId));
}

export async function getCouponWithProducts(couponId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get coupon
  const coupon = await db
    .select()
    .from(coupons)
    .where(eq(coupons.id, couponId))
    .limit(1);

  if (!coupon.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(coupon[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get coupon products
  const couponProductsData = await db
    .select({
      product: products,
    })
    .from(couponProducts)
    .innerJoin(products, eq(couponProducts.productId, products.id))
    .where(eq(couponProducts.couponId, couponId));

  return {
    ...coupon[0],
    products: couponProductsData.map(cp => cp.product),
  };
}

export async function validateCoupon(code: string, organizationId: string, productId?: string) {
  // Get coupon
  const coupon = await db
    .select()
    .from(coupons)
    .where(and(
      eq(coupons.code, code.toUpperCase()),
      eq(coupons.organizationId, organizationId),
      eq(coupons.isActive, true)
    ))
    .limit(1);

  if (!coupon.length) {
    return { valid: false, error: 'Coupon not found' };
  }

  const couponData = coupon[0];

  // Check if coupon is expired
  if (couponData.validUntil && new Date() > couponData.validUntil) {
    return { valid: false, error: 'Coupon has expired' };
  }

  // Check if coupon has reached max uses
  if (couponData.maxUses && couponData.usedCount >= couponData.maxUses) {
    return { valid: false, error: 'Coupon usage limit reached' };
  }

  // Check if coupon applies to the specific product (if productId provided)
  if (productId) {
    const applicableProduct = await db
      .select()
      .from(couponProducts)
      .where(and(
        eq(couponProducts.couponId, couponData.id),
        eq(couponProducts.productId, productId)
      ))
      .limit(1);

    if (!applicableProduct.length) {
      return { valid: false, error: 'Coupon not applicable to this product' };
    }
  }

  return { 
    valid: true, 
    coupon: couponData,
    discountType: couponData.discountType,
    discountValue: parseFloat(couponData.discountValue)
  };
}

export async function incrementCouponUsage(couponId: string) {
  await db
    .update(coupons)
    .set({
      usedCount: db.select().from(coupons).where(eq(coupons.id, couponId)).then(c => c[0].usedCount + 1),
      updatedAt: new Date(),
    })
    .where(eq(coupons.id, couponId));
}
