'use server';

import { db } from '@/db';
import { organizations, teams, courses, courseModules, modules } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and, inArray } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createCourse(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const category = formData.get('category') as string;
  const duration = formData.get('duration') as string;
  const moduleIds = formData.getAll('moduleIds') as string[];

  if (!title) {
    throw new Error('Title is required');
  }

  const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  try {
    // Create course
    const [newCourse] = await db
      .insert(courses)
      .values({
        organizationId,
        title,
        description,
        category,
        slug,
        duration,
      })
      .returning();

    // Link modules to course if provided
    if (moduleIds.length > 0) {
      const courseModuleData = moduleIds.map((moduleId, index) => ({
        courseId: newCourse.id,
        moduleId,
        sortOrder: index,
      }));

      await db.insert(courseModules).values(courseModuleData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/courses`);
    redirect(`/${org[0].slug}/courses/${newCourse.slug}`);
  } catch (error) {
    console.error('Error creating course:', error);
    throw new Error('Failed to create course');
  }
}

export async function updateCourse(courseId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get course and check access
  const course = await db
    .select()
    .from(courses)
    .where(eq(courses.id, courseId))
    .limit(1);

  if (!course.length) {
    throw new Error('Course not found');
  }

  const hasAccess = await checkOrganizationAccess(course[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const category = formData.get('category') as string;
  const duration = formData.get('duration') as string;
  const isPublished = formData.get('isPublished') === 'true';
  const moduleIds = formData.getAll('moduleIds') as string[];

  if (!title) {
    throw new Error('Title is required');
  }

  const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  try {
    // Update course
    await db
      .update(courses)
      .set({
        title,
        description,
        category,
        slug,
        duration,
        isPublished,
        updatedAt: new Date(),
      })
      .where(eq(courses.id, courseId));

    // Update course modules
    // First, remove existing associations
    await db
      .delete(courseModules)
      .where(eq(courseModules.courseId, courseId));

    // Then add new associations
    if (moduleIds.length > 0) {
      const courseModuleData = moduleIds.map((moduleId, index) => ({
        courseId,
        moduleId,
        sortOrder: index,
      }));

      await db.insert(courseModules).values(courseModuleData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, course[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/courses`);
    revalidatePath(`/${org[0].slug}/courses/${slug}`);
  } catch (error) {
    console.error('Error updating course:', error);
    throw new Error('Failed to update course');
  }
}

export async function deleteCourse(courseId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get course and check access
  const course = await db
    .select()
    .from(courses)
    .where(eq(courses.id, courseId))
    .limit(1);

  if (!course.length) {
    throw new Error('Course not found');
  }

  const hasAccess = await checkOrganizationAccess(course[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete course (cascade will handle related records)
  await db
    .delete(courses)
    .where(eq(courses.id, courseId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, course[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/courses`);
  redirect(`/${org[0].slug}/courses`);
}

export async function getCourses(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(courses)
    .where(eq(courses.organizationId, organizationId));
}

export async function getCourseBySlug(organizationId: string, slug: string) {
  const result = await db
    .select()
    .from(courses)
    .where(and(
      eq(courses.organizationId, organizationId),
      eq(courses.slug, slug)
    ))
    .limit(1);

  return result[0] || null;
}

export async function getCourseWithModules(courseId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get course
  const course = await db
    .select()
    .from(courses)
    .where(eq(courses.id, courseId))
    .limit(1);

  if (!course.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(course[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get course modules
  const courseModulesData = await db
    .select({
      module: modules,
      sortOrder: courseModules.sortOrder,
    })
    .from(courseModules)
    .innerJoin(modules, eq(courseModules.moduleId, modules.id))
    .where(eq(courseModules.courseId, courseId))
    .orderBy(courseModules.sortOrder);

  return {
    ...course[0],
    modules: courseModulesData.map(cm => ({ ...cm.module, sortOrder: cm.sortOrder })),
  };
}

export async function reorderCourseModules(courseId: string, moduleIds: string[]) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get course and check access
  const course = await db
    .select()
    .from(courses)
    .where(eq(courses.id, courseId))
    .limit(1);

  if (!course.length) {
    throw new Error('Course not found');
  }

  const hasAccess = await checkOrganizationAccess(course[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Update sort orders
  for (let i = 0; i < moduleIds.length; i++) {
    await db
      .update(courseModules)
      .set({ sortOrder: i })
      .where(and(
        eq(courseModules.courseId, courseId),
        eq(courseModules.moduleId, moduleIds[i])
      ));
  }

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, course[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/courses/${course[0].slug}`);
}
