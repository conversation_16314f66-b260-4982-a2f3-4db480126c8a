'use server';

import { db } from '@/db';
import { organizations, teams, modules, moduleLessons, lessons } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createModule(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const lessonIds = formData.getAll('lessonIds') as string[];

  if (!title) {
    throw new Error('Title is required');
  }

  try {
    // Create module
    const [newModule] = await db
      .insert(modules)
      .values({
        organizationId,
        title,
        description,
      })
      .returning();

    // Link lessons to module if provided
    if (lessonIds.length > 0) {
      const moduleLessonData = lessonIds.map((lessonId, index) => ({
        moduleId: newModule.id,
        lessonId,
        sortOrder: index,
      }));

      await db.insert(moduleLessons).values(moduleLessonData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/modules`);
    redirect(`/${org[0].slug}/modules`);
  } catch (error) {
    console.error('Error creating module:', error);
    throw new Error('Failed to create module');
  }
}

export async function updateModule(moduleId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get module and check access
  const module = await db
    .select()
    .from(modules)
    .where(eq(modules.id, moduleId))
    .limit(1);

  if (!module.length) {
    throw new Error('Module not found');
  }

  const hasAccess = await checkOrganizationAccess(module[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const isPublished = formData.get('isPublished') === 'true';
  const lessonIds = formData.getAll('lessonIds') as string[];

  if (!title) {
    throw new Error('Title is required');
  }

  try {
    // Update module
    await db
      .update(modules)
      .set({
        title,
        description,
        isPublished,
        updatedAt: new Date(),
      })
      .where(eq(modules.id, moduleId));

    // Update module lessons
    // First, remove existing associations
    await db
      .delete(moduleLessons)
      .where(eq(moduleLessons.moduleId, moduleId));

    // Then add new associations
    if (lessonIds.length > 0) {
      const moduleLessonData = lessonIds.map((lessonId, index) => ({
        moduleId,
        lessonId,
        sortOrder: index,
      }));

      await db.insert(moduleLessons).values(moduleLessonData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, module[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/modules`);
  } catch (error) {
    console.error('Error updating module:', error);
    throw new Error('Failed to update module');
  }
}

export async function deleteModule(moduleId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get module and check access
  const module = await db
    .select()
    .from(modules)
    .where(eq(modules.id, moduleId))
    .limit(1);

  if (!module.length) {
    throw new Error('Module not found');
  }

  const hasAccess = await checkOrganizationAccess(module[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete module (cascade will handle related records)
  await db
    .delete(modules)
    .where(eq(modules.id, moduleId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, module[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/modules`);
}

export async function getModules(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(modules)
    .where(eq(modules.organizationId, organizationId));
}

export async function getModuleWithLessons(moduleId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get module
  const module = await db
    .select()
    .from(modules)
    .where(eq(modules.id, moduleId))
    .limit(1);

  if (!module.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(module[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get module lessons
  const moduleLessonsData = await db
    .select({
      lesson: lessons,
      sortOrder: moduleLessons.sortOrder,
    })
    .from(moduleLessons)
    .innerJoin(lessons, eq(moduleLessons.lessonId, lessons.id))
    .where(eq(moduleLessons.moduleId, moduleId))
    .orderBy(moduleLessons.sortOrder);

  return {
    ...module[0],
    lessons: moduleLessonsData.map(ml => ({ ...ml.lesson, sortOrder: ml.sortOrder })),
  };
}

export async function reorderModuleLessons(moduleId: string, lessonIds: string[]) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get module and check access
  const module = await db
    .select()
    .from(modules)
    .where(eq(modules.id, moduleId))
    .limit(1);

  if (!module.length) {
    throw new Error('Module not found');
  }

  const hasAccess = await checkOrganizationAccess(module[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Update sort orders
  for (let i = 0; i < lessonIds.length; i++) {
    await db
      .update(moduleLessons)
      .set({ sortOrder: i })
      .where(and(
        eq(moduleLessons.moduleId, moduleId),
        eq(moduleLessons.lessonId, lessonIds[i])
      ));
  }

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, module[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/modules`);
}

export async function getAvailableModulesForCourse(organizationId: string, courseId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get all modules for the organization
  const allModules = await db
    .select()
    .from(modules)
    .where(eq(modules.organizationId, organizationId));

  return allModules;
}
