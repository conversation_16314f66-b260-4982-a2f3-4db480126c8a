'use server';

import { db } from '@/db';
import { organizations, teams, landingPages, products } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createLandingPage(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const productId = formData.get('productId') as string;
  const customDomain = formData.get('customDomain') as string;
  const seoTitle = formData.get('seoTitle') as string;
  const seoDescription = formData.get('seoDescription') as string;

  if (!title || !productId) {
    throw new Error('Title and product are required');
  }

  // Generate slug from title
  const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  try {
    // Create landing page
    const [newLandingPage] = await db
      .insert(landingPages)
      .values({
        organizationId,
        productId,
        title,
        description,
        slug,
        customDomain,
        seoTitle: seoTitle || title,
        seoDescription: seoDescription || description,
        content: {}, // Empty content object for page builder
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/landing-pages`);
    redirect(`/${org[0].slug}/landing-pages/${newLandingPage.slug}`);
  } catch (error) {
    console.error('Error creating landing page:', error);
    if (error instanceof Error && error.message.includes('unique')) {
      throw new Error('Landing page slug already exists');
    }
    throw new Error('Failed to create landing page');
  }
}

export async function updateLandingPage(landingPageId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get landing page and check access
  const landingPage = await db
    .select()
    .from(landingPages)
    .where(eq(landingPages.id, landingPageId))
    .limit(1);

  if (!landingPage.length) {
    throw new Error('Landing page not found');
  }

  const hasAccess = await checkOrganizationAccess(landingPage[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const productId = formData.get('productId') as string;
  const customDomain = formData.get('customDomain') as string;
  const seoTitle = formData.get('seoTitle') as string;
  const seoDescription = formData.get('seoDescription') as string;
  const isPublished = formData.get('isPublished') === 'true';

  if (!title || !productId) {
    throw new Error('Title and product are required');
  }

  const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  try {
    // Update landing page
    await db
      .update(landingPages)
      .set({
        productId,
        title,
        description,
        slug,
        customDomain,
        seoTitle: seoTitle || title,
        seoDescription: seoDescription || description,
        isPublished,
        updatedAt: new Date(),
      })
      .where(eq(landingPages.id, landingPageId));

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, landingPage[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/landing-pages`);
    revalidatePath(`/${org[0].slug}/landing-pages/${slug}`);
  } catch (error) {
    console.error('Error updating landing page:', error);
    if (error instanceof Error && error.message.includes('unique')) {
      throw new Error('Landing page slug already exists');
    }
    throw new Error('Failed to update landing page');
  }
}

export async function updateLandingPageContent(landingPageId: string, content: any) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get landing page and check access
  const landingPage = await db
    .select()
    .from(landingPages)
    .where(eq(landingPages.id, landingPageId))
    .limit(1);

  if (!landingPage.length) {
    throw new Error('Landing page not found');
  }

  const hasAccess = await checkOrganizationAccess(landingPage[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  try {
    // Update landing page content
    await db
      .update(landingPages)
      .set({
        content,
        updatedAt: new Date(),
      })
      .where(eq(landingPages.id, landingPageId));

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, landingPage[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/landing-pages/${landingPage[0].slug}`);
  } catch (error) {
    console.error('Error updating landing page content:', error);
    throw new Error('Failed to update landing page content');
  }
}

export async function deleteLandingPage(landingPageId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get landing page and check access
  const landingPage = await db
    .select()
    .from(landingPages)
    .where(eq(landingPages.id, landingPageId))
    .limit(1);

  if (!landingPage.length) {
    throw new Error('Landing page not found');
  }

  const hasAccess = await checkOrganizationAccess(landingPage[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete landing page
  await db
    .delete(landingPages)
    .where(eq(landingPages.id, landingPageId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, landingPage[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/landing-pages`);
  redirect(`/${org[0].slug}/landing-pages`);
}

export async function getLandingPages(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select({
      landingPage: landingPages,
      product: products,
    })
    .from(landingPages)
    .innerJoin(products, eq(landingPages.productId, products.id))
    .where(eq(landingPages.organizationId, organizationId));
}

export async function getLandingPageBySlug(organizationId: string, slug: string) {
  const result = await db
    .select({
      landingPage: landingPages,
      product: products,
    })
    .from(landingPages)
    .innerJoin(products, eq(landingPages.productId, products.id))
    .where(and(
      eq(landingPages.organizationId, organizationId),
      eq(landingPages.slug, slug)
    ))
    .limit(1);

  return result[0] || null;
}

export async function getPublicLandingPageBySlug(slug: string) {
  const result = await db
    .select({
      landingPage: landingPages,
      product: products,
      organization: organizations,
    })
    .from(landingPages)
    .innerJoin(products, eq(landingPages.productId, products.id))
    .innerJoin(organizations, eq(landingPages.organizationId, organizations.id))
    .where(and(
      eq(landingPages.slug, slug),
      eq(landingPages.isPublished, true)
    ))
    .limit(1);

  return result[0] || null;
}

export async function getAvailableProductsForLandingPage(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get all active products for the organization
  return await db
    .select()
    .from(products)
    .where(and(
      eq(products.organizationId, organizationId),
      eq(products.isActive, true)
    ));
}

export async function duplicateLandingPage(landingPageId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get landing page and check access
  const landingPage = await db
    .select()
    .from(landingPages)
    .where(eq(landingPages.id, landingPageId))
    .limit(1);

  if (!landingPage.length) {
    throw new Error('Landing page not found');
  }

  const hasAccess = await checkOrganizationAccess(landingPage[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  try {
    const originalPage = landingPage[0];
    const newSlug = `${originalPage.slug}-copy`;

    // Create duplicate landing page
    const [duplicatedPage] = await db
      .insert(landingPages)
      .values({
        organizationId: originalPage.organizationId,
        productId: originalPage.productId,
        title: `${originalPage.title} (Copy)`,
        description: originalPage.description,
        slug: newSlug,
        content: originalPage.content,
        isPublished: false, // Duplicated pages start as unpublished
        customDomain: null, // Don't duplicate custom domain
        seoTitle: originalPage.seoTitle,
        seoDescription: originalPage.seoDescription,
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, originalPage.organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/landing-pages`);
    
    return duplicatedPage;
  } catch (error) {
    console.error('Error duplicating landing page:', error);
    throw new Error('Failed to duplicate landing page');
  }
}
