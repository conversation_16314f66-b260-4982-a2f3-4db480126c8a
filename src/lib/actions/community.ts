'use server';

import { db } from '@/db';
import { 
  organizations, 
  teams, 
  communityCategories, 
  communityPosts, 
  communityReplies, 
  communityLikes,
  subscribers 
} from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and, desc, sql } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';

async function checkCommunityAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return { hasAccess: true, role: 'admin' };

  // Check if user is team member
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  if (teamMember.length) return { hasAccess: true, role: teamMember[0].role };

  // Check if user is subscriber
  const subscriber = await db
    .select()
    .from(subscribers)
    .where(and(
      eq(subscribers.organizationId, organizationId),
      eq(subscribers.userId, userId),
      eq(subscribers.status, 'active')
    ))
    .limit(1);

  if (subscriber.length) return { hasAccess: true, role: 'subscriber' };

  return { hasAccess: false, role: null };
}

// Community Categories
export async function createCommunityCategory(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const access = await checkCommunityAccess(organizationId, userId);
  if (!access.hasAccess || (access.role !== 'admin' && access.role !== 'editor')) {
    throw new Error('Unauthorized');
  }

  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const color = formData.get('color') as string;

  if (!name) {
    throw new Error('Category name is required');
  }

  try {
    const [newCategory] = await db
      .insert(communityCategories)
      .values({
        organizationId,
        name,
        description,
        color,
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/community`);
    return newCategory;
  } catch (error) {
    console.error('Error creating community category:', error);
    throw new Error('Failed to create category');
  }
}

export async function getCommunityCategories(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const access = await checkCommunityAccess(organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(communityCategories)
    .where(and(
      eq(communityCategories.organizationId, organizationId),
      eq(communityCategories.isActive, true)
    ))
    .orderBy(communityCategories.sortOrder);
}

// Community Posts
export async function createCommunityPost(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const access = await checkCommunityAccess(organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  const categoryId = formData.get('categoryId') as string;
  const title = formData.get('title') as string;
  const content = formData.get('content') as string;

  if (!categoryId || !title || !content) {
    throw new Error('Category, title, and content are required');
  }

  try {
    const [newPost] = await db
      .insert(communityPosts)
      .values({
        organizationId,
        categoryId,
        authorId: userId,
        title,
        content,
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/community`);
    return newPost;
  } catch (error) {
    console.error('Error creating community post:', error);
    throw new Error('Failed to create post');
  }
}

export async function getCommunityPosts(organizationId: string, categoryId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const access = await checkCommunityAccess(organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  let query = db
    .select({
      post: communityPosts,
      category: communityCategories,
    })
    .from(communityPosts)
    .innerJoin(communityCategories, eq(communityPosts.categoryId, communityCategories.id))
    .where(eq(communityPosts.organizationId, organizationId));

  if (categoryId) {
    query = query.where(eq(communityPosts.categoryId, categoryId));
  }

  return await query
    .orderBy(desc(communityPosts.isPinned), desc(communityPosts.lastReplyAt), desc(communityPosts.createdAt));
}

export async function getCommunityPost(postId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const post = await db
    .select({
      post: communityPosts,
      category: communityCategories,
    })
    .from(communityPosts)
    .innerJoin(communityCategories, eq(communityPosts.categoryId, communityCategories.id))
    .where(eq(communityPosts.id, postId))
    .limit(1);

  if (!post.length) {
    return null;
  }

  const access = await checkCommunityAccess(post[0].post.organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  // Increment view count
  await db
    .update(communityPosts)
    .set({
      viewCount: sql`${communityPosts.viewCount} + 1`,
    })
    .where(eq(communityPosts.id, postId));

  return post[0];
}

// Community Replies
export async function createCommunityReply(postId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const content = formData.get('content') as string;
  const parentReplyId = formData.get('parentReplyId') as string || null;

  if (!content) {
    throw new Error('Content is required');
  }

  // Get post to check access
  const post = await db
    .select()
    .from(communityPosts)
    .where(eq(communityPosts.id, postId))
    .limit(1);

  if (!post.length) {
    throw new Error('Post not found');
  }

  const access = await checkCommunityAccess(post[0].organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  if (post[0].isLocked) {
    throw new Error('This post is locked and cannot receive new replies');
  }

  try {
    const [newReply] = await db
      .insert(communityReplies)
      .values({
        postId,
        authorId: userId,
        content,
        parentReplyId,
      })
      .returning();

    // Update post reply count and last reply info
    await db
      .update(communityPosts)
      .set({
        replyCount: sql`${communityPosts.replyCount} + 1`,
        lastReplyAt: new Date(),
        lastReplyBy: userId,
      })
      .where(eq(communityPosts.id, postId));

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, post[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/community/posts/${postId}`);
    return newReply;
  } catch (error) {
    console.error('Error creating community reply:', error);
    throw new Error('Failed to create reply');
  }
}

export async function getCommunityReplies(postId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get post to check access
  const post = await db
    .select()
    .from(communityPosts)
    .where(eq(communityPosts.id, postId))
    .limit(1);

  if (!post.length) {
    throw new Error('Post not found');
  }

  const access = await checkCommunityAccess(post[0].organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(communityReplies)
    .where(eq(communityReplies.postId, postId))
    .orderBy(communityReplies.createdAt);
}

// Community Likes
export async function toggleCommunityLike(postId?: string, replyId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  if (!postId && !replyId) {
    throw new Error('Either postId or replyId is required');
  }

  // Get organization ID for access check
  let organizationId: string;
  
  if (postId) {
    const post = await db
      .select()
      .from(communityPosts)
      .where(eq(communityPosts.id, postId))
      .limit(1);
    
    if (!post.length) {
      throw new Error('Post not found');
    }
    organizationId = post[0].organizationId;
  } else if (replyId) {
    const reply = await db
      .select({
        reply: communityReplies,
        post: communityPosts,
      })
      .from(communityReplies)
      .innerJoin(communityPosts, eq(communityReplies.postId, communityPosts.id))
      .where(eq(communityReplies.id, replyId))
      .limit(1);
    
    if (!reply.length) {
      throw new Error('Reply not found');
    }
    organizationId = reply[0].post.organizationId;
  } else {
    throw new Error('Invalid request');
  }

  const access = await checkCommunityAccess(organizationId, userId);
  if (!access.hasAccess) {
    throw new Error('Unauthorized');
  }

  // Check if like already exists
  const existingLike = await db
    .select()
    .from(communityLikes)
    .where(and(
      eq(communityLikes.userId, userId),
      postId ? eq(communityLikes.postId, postId) : sql`${communityLikes.postId} IS NULL`,
      replyId ? eq(communityLikes.replyId, replyId) : sql`${communityLikes.replyId} IS NULL`
    ))
    .limit(1);

  if (existingLike.length) {
    // Remove like
    await db
      .delete(communityLikes)
      .where(eq(communityLikes.id, existingLike[0].id));

    // Decrement like count
    if (postId) {
      await db
        .update(communityPosts)
        .set({
          likeCount: sql`${communityPosts.likeCount} - 1`,
        })
        .where(eq(communityPosts.id, postId));
    } else if (replyId) {
      await db
        .update(communityReplies)
        .set({
          likeCount: sql`${communityReplies.likeCount} - 1`,
        })
        .where(eq(communityReplies.id, replyId));
    }

    return { liked: false };
  } else {
    // Add like
    await db
      .insert(communityLikes)
      .values({
        userId,
        postId: postId || null,
        replyId: replyId || null,
      });

    // Increment like count
    if (postId) {
      await db
        .update(communityPosts)
        .set({
          likeCount: sql`${communityPosts.likeCount} + 1`,
        })
        .where(eq(communityPosts.id, postId));
    } else if (replyId) {
      await db
        .update(communityReplies)
        .set({
          likeCount: sql`${communityReplies.likeCount} + 1`,
        })
        .where(eq(communityReplies.id, replyId));
    }

    return { liked: true };
  }
}
