'use server';

import { db } from '@/db';
import { organizations, teams, lessons } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createLesson(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const content = formData.get('content') as string;
  const videoUrl = formData.get('videoUrl') as string;
  const lessonType = formData.get('lessonType') as 'video' | 'text' | 'quiz';
  const duration = formData.get('duration') as string;

  if (!title) {
    throw new Error('Title is required');
  }

  try {
    // Create lesson
    const [newLesson] = await db
      .insert(lessons)
      .values({
        organizationId,
        title,
        description,
        content,
        videoUrl,
        lessonType: lessonType || 'video',
        duration,
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/lessons`);
    redirect(`/${org[0].slug}/lessons`);
  } catch (error) {
    console.error('Error creating lesson:', error);
    throw new Error('Failed to create lesson');
  }
}

export async function updateLesson(lessonId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get lesson and check access
  const lesson = await db
    .select()
    .from(lessons)
    .where(eq(lessons.id, lessonId))
    .limit(1);

  if (!lesson.length) {
    throw new Error('Lesson not found');
  }

  const hasAccess = await checkOrganizationAccess(lesson[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const content = formData.get('content') as string;
  const videoUrl = formData.get('videoUrl') as string;
  const lessonType = formData.get('lessonType') as 'video' | 'text' | 'quiz';
  const duration = formData.get('duration') as string;
  const isPublished = formData.get('isPublished') === 'true';

  if (!title) {
    throw new Error('Title is required');
  }

  try {
    // Update lesson
    await db
      .update(lessons)
      .set({
        title,
        description,
        content,
        videoUrl,
        lessonType: lessonType || 'video',
        duration,
        isPublished,
        updatedAt: new Date(),
      })
      .where(eq(lessons.id, lessonId));

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, lesson[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/lessons`);
  } catch (error) {
    console.error('Error updating lesson:', error);
    throw new Error('Failed to update lesson');
  }
}

export async function deleteLesson(lessonId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get lesson and check access
  const lesson = await db
    .select()
    .from(lessons)
    .where(eq(lessons.id, lessonId))
    .limit(1);

  if (!lesson.length) {
    throw new Error('Lesson not found');
  }

  const hasAccess = await checkOrganizationAccess(lesson[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete lesson (cascade will handle related records)
  await db
    .delete(lessons)
    .where(eq(lessons.id, lessonId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, lesson[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/lessons`);
}

export async function getLessons(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(lessons)
    .where(eq(lessons.organizationId, organizationId));
}

export async function getLesson(lessonId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get lesson
  const lesson = await db
    .select()
    .from(lessons)
    .where(eq(lessons.id, lessonId))
    .limit(1);

  if (!lesson.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(lesson[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return lesson[0];
}

export async function getAvailableLessonsForModule(organizationId: string, moduleId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get all lessons for the organization
  const allLessons = await db
    .select()
    .from(lessons)
    .where(eq(lessons.organizationId, organizationId));

  return allLessons;
}

export async function duplicateLesson(lessonId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get lesson and check access
  const lesson = await db
    .select()
    .from(lessons)
    .where(eq(lessons.id, lessonId))
    .limit(1);

  if (!lesson.length) {
    throw new Error('Lesson not found');
  }

  const hasAccess = await checkOrganizationAccess(lesson[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  try {
    // Create duplicate lesson
    const [duplicatedLesson] = await db
      .insert(lessons)
      .values({
        organizationId: lesson[0].organizationId,
        title: `${lesson[0].title} (Copy)`,
        description: lesson[0].description,
        content: lesson[0].content,
        videoUrl: lesson[0].videoUrl,
        lessonType: lesson[0].lessonType,
        duration: lesson[0].duration,
        isPublished: false, // Duplicated lessons start as unpublished
      })
      .returning();

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, lesson[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/lessons`);
    
    return duplicatedLesson;
  } catch (error) {
    console.error('Error duplicating lesson:', error);
    throw new Error('Failed to duplicate lesson');
  }
}
