'use server';

import { db } from '@/db';
import { organizations, teams } from '@/schema';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';

export async function inviteTeamMember(organizationId: string, email: string, role: 'admin' | 'editor' = 'editor') {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user is owner or admin of the organization
  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (!org.length) {
    throw new Error('Organization not found');
  }

  const isOwner = org[0].ownerId === userId;
  
  if (!isOwner) {
    // Check if user is admin team member
    const teamMember = await db
      .select()
      .from(teams)
      .where(and(
        eq(teams.organizationId, organizationId),
        eq(teams.userId, userId),
        eq(teams.role, 'admin'),
        eq(teams.isActive, true)
      ))
      .limit(1);

    if (!teamMember.length) {
      throw new Error('Unauthorized');
    }
  }

  try {
    // Send invitation through Clerk
    const invitation = await clerkClient.organizations.createOrganizationInvitation({
      organizationId: org[0].clerkOrgId,
      emailAddress: email,
      role: role === 'admin' ? 'org:admin' : 'org:member',
      inviterUserId: userId,
    });

    // Note: We'll add the team member to our database when they accept the invitation
    // This should be handled in a webhook or when they first access the organization

    revalidatePath(`/${org[0].slug}/teams`);
    
    return { success: true, invitationId: invitation.id };
  } catch (error) {
    console.error('Error inviting team member:', error);
    throw new Error('Failed to send invitation');
  }
}

export async function addTeamMember(organizationId: string, userId: string, role: 'admin' | 'editor', invitedBy: string) {
  // This function is typically called from a webhook when a user accepts an invitation
  
  // Check if team member already exists
  const existingMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId)
    ))
    .limit(1);

  if (existingMember.length) {
    // Update existing member
    await db
      .update(teams)
      .set({
        role,
        isActive: true,
        joinedAt: new Date(),
      })
      .where(and(
        eq(teams.organizationId, organizationId),
        eq(teams.userId, userId)
      ));
  } else {
    // Add new team member
    await db
      .insert(teams)
      .values({
        organizationId,
        userId,
        role,
        invitedBy,
      });
  }

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (org.length) {
    revalidatePath(`/${org[0].slug}/teams`);
  }
}

export async function updateTeamMemberRole(organizationId: string, memberId: string, newRole: 'admin' | 'editor') {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user is owner or admin of the organization
  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (!org.length) {
    throw new Error('Organization not found');
  }

  const isOwner = org[0].ownerId === userId;
  
  if (!isOwner) {
    // Check if user is admin team member
    const teamMember = await db
      .select()
      .from(teams)
      .where(and(
        eq(teams.organizationId, organizationId),
        eq(teams.userId, userId),
        eq(teams.role, 'admin'),
        eq(teams.isActive, true)
      ))
      .limit(1);

    if (!teamMember.length) {
      throw new Error('Unauthorized');
    }
  }

  await db
    .update(teams)
    .set({
      role: newRole,
    })
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, memberId)
    ));

  revalidatePath(`/${org[0].slug}/teams`);
}

export async function removeTeamMember(organizationId: string, memberId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user is owner or admin of the organization
  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (!org.length) {
    throw new Error('Organization not found');
  }

  const isOwner = org[0].ownerId === userId;
  
  if (!isOwner) {
    // Check if user is admin team member
    const teamMember = await db
      .select()
      .from(teams)
      .where(and(
        eq(teams.organizationId, organizationId),
        eq(teams.userId, userId),
        eq(teams.role, 'admin'),
        eq(teams.isActive, true)
      ))
      .limit(1);

    if (!teamMember.length) {
      throw new Error('Unauthorized');
    }
  }

  // Don't allow removing the owner
  if (memberId === org[0].ownerId) {
    throw new Error('Cannot remove organization owner');
  }

  // Deactivate team member
  await db
    .update(teams)
    .set({
      isActive: false,
    })
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, memberId)
    ));

  // Remove from Clerk organization
  try {
    await clerkClient.organizations.deleteOrganizationMembership({
      organizationId: org[0].clerkOrgId,
      userId: memberId,
    });
  } catch (error) {
    console.error('Error removing from Clerk organization:', error);
  }

  revalidatePath(`/${org[0].slug}/teams`);
}

export async function getTeamMembers(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user has access to this organization
  const hasAccess = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (!hasAccess.length) {
    // Check if user is team member
    const teamMember = await db
      .select()
      .from(teams)
      .where(and(
        eq(teams.organizationId, organizationId),
        eq(teams.userId, userId),
        eq(teams.isActive, true)
      ))
      .limit(1);

    if (!teamMember.length) {
      throw new Error('Unauthorized');
    }
  }

  return await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.isActive, true)
    ));
}
