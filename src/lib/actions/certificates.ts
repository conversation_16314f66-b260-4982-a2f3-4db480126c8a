'use server';

import { db } from '@/db';
import { organizations, teams, certificates, certificateCourses, userCertificates, courses } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createCertificate(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const signerName = formData.get('signerName') as string;
  const signerPosition = formData.get('signerPosition') as string;
  const template = formData.get('template') as string;
  const backgroundImage = formData.get('backgroundImage') as string;
  const courseIds = formData.getAll('courseIds') as string[];

  if (!title || !signerName || !signerPosition) {
    throw new Error('Title, signer name, and signer position are required');
  }

  try {
    // Create certificate
    const [newCertificate] = await db
      .insert(certificates)
      .values({
        organizationId,
        title,
        description,
        signerName,
        signerPosition,
        template,
        backgroundImage,
      })
      .returning();

    // Link courses to certificate if provided
    if (courseIds.length > 0) {
      const certificateCourseData = courseIds.map((courseId) => ({
        certificateId: newCertificate.id,
        courseId,
      }));

      await db.insert(certificateCourses).values(certificateCourseData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/certificates`);
    redirect(`/${org[0].slug}/certificates`);
  } catch (error) {
    console.error('Error creating certificate:', error);
    throw new Error('Failed to create certificate');
  }
}

export async function updateCertificate(certificateId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get certificate and check access
  const certificate = await db
    .select()
    .from(certificates)
    .where(eq(certificates.id, certificateId))
    .limit(1);

  if (!certificate.length) {
    throw new Error('Certificate not found');
  }

  const hasAccess = await checkOrganizationAccess(certificate[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const title = formData.get('title') as string;
  const description = formData.get('description') as string;
  const signerName = formData.get('signerName') as string;
  const signerPosition = formData.get('signerPosition') as string;
  const template = formData.get('template') as string;
  const backgroundImage = formData.get('backgroundImage') as string;
  const isActive = formData.get('isActive') === 'true';
  const courseIds = formData.getAll('courseIds') as string[];

  if (!title || !signerName || !signerPosition) {
    throw new Error('Title, signer name, and signer position are required');
  }

  try {
    // Update certificate
    await db
      .update(certificates)
      .set({
        title,
        description,
        signerName,
        signerPosition,
        template,
        backgroundImage,
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(certificates.id, certificateId));

    // Update certificate courses
    // First, remove existing associations
    await db
      .delete(certificateCourses)
      .where(eq(certificateCourses.certificateId, certificateId));

    // Then add new associations
    if (courseIds.length > 0) {
      const certificateCourseData = courseIds.map((courseId) => ({
        certificateId,
        courseId,
      }));

      await db.insert(certificateCourses).values(certificateCourseData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, certificate[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/certificates`);
  } catch (error) {
    console.error('Error updating certificate:', error);
    throw new Error('Failed to update certificate');
  }
}

export async function deleteCertificate(certificateId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get certificate and check access
  const certificate = await db
    .select()
    .from(certificates)
    .where(eq(certificates.id, certificateId))
    .limit(1);

  if (!certificate.length) {
    throw new Error('Certificate not found');
  }

  const hasAccess = await checkOrganizationAccess(certificate[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete certificate (cascade will handle related records)
  await db
    .delete(certificates)
    .where(eq(certificates.id, certificateId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, certificate[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/certificates`);
}

export async function getCertificates(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(certificates)
    .where(eq(certificates.organizationId, organizationId));
}

export async function getCertificateWithCourses(certificateId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get certificate
  const certificate = await db
    .select()
    .from(certificates)
    .where(eq(certificates.id, certificateId))
    .limit(1);

  if (!certificate.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(certificate[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get certificate courses
  const certificateCoursesData = await db
    .select({
      course: courses,
    })
    .from(certificateCourses)
    .innerJoin(courses, eq(certificateCourses.courseId, courses.id))
    .where(eq(certificateCourses.certificateId, certificateId));

  return {
    ...certificate[0],
    courses: certificateCoursesData.map(cc => cc.course),
  };
}

export async function issueCertificate(userId: string, courseId: string, certificateId: string) {
  // Check if user has completed the course (this would need to be implemented based on your progress tracking)
  // For now, we'll assume the course is completed
  
  // Check if certificate already exists for this user and course
  const existingCertificate = await db
    .select()
    .from(userCertificates)
    .where(and(
      eq(userCertificates.userId, userId),
      eq(userCertificates.courseId, courseId),
      eq(userCertificates.certificateId, certificateId)
    ))
    .limit(1);

  if (existingCertificate.length) {
    return existingCertificate[0];
  }

  // Get course to get organization ID
  const course = await db
    .select()
    .from(courses)
    .where(eq(courses.id, courseId))
    .limit(1);

  if (!course.length) {
    throw new Error('Course not found');
  }

  // Issue new certificate
  const [newUserCertificate] = await db
    .insert(userCertificates)
    .values({
      userId,
      certificateId,
      courseId,
      organizationId: course[0].organizationId,
      // certificateUrl would be generated by a certificate generation service
    })
    .returning();

  return newUserCertificate;
}

export async function getUserCertificates(userId: string, organizationId?: string) {
  let query = db
    .select({
      userCertificate: userCertificates,
      certificate: certificates,
      course: courses,
    })
    .from(userCertificates)
    .innerJoin(certificates, eq(userCertificates.certificateId, certificates.id))
    .innerJoin(courses, eq(userCertificates.courseId, courses.id))
    .where(eq(userCertificates.userId, userId));

  if (organizationId) {
    query = query.where(eq(userCertificates.organizationId, organizationId));
  }

  return await query;
}

export async function getAvailableCoursesForCertificate(organizationId: string, certificateId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get all courses for the organization
  const allCourses = await db
    .select()
    .from(courses)
    .where(eq(courses.organizationId, organizationId));

  return allCourses;
}
