'use server';

import { db } from '@/db';
import { organizations, teams } from '@/schema';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

// Plan limits
const PLAN_LIMITS = {
  basic: 1,
  professional: 3,
  pro: 10,
  business: 30,
} as const;

export async function createOrganization(formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const category = formData.get('category') as string;
  const planType = formData.get('planType') as 'basic' | 'professional' | 'pro' | 'business';

  if (!name || !planType) {
    throw new Error('Name and plan type are required');
  }

  // Check if user has reached organization limit for their plan
  const existingOrgs = await db
    .select()
    .from(organizations)
    .where(eq(organizations.ownerId, userId));

  if (existingOrgs.length >= PLAN_LIMITS[planType]) {
    throw new Error(`Plan limit reached. ${planType} plan allows up to ${PLAN_LIMITS[planType]} organizations.`);
  }

  // Create Clerk organization
  const clerkOrg = await clerkClient.organizations.createOrganization({
    name,
    createdBy: userId,
  });

  // Generate slug from name
  const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  try {
    // Create organization in database
    const [newOrg] = await db
      .insert(organizations)
      .values({
        clerkOrgId: clerkOrg.id,
        name,
        description,
        slug,
        category,
        ownerId: userId,
        planType,
      })
      .returning();

    // Add owner as admin team member
    await db.insert(teams).values({
      organizationId: newOrg.id,
      userId,
      role: 'admin',
      invitedBy: userId,
    });

    revalidatePath('/organizations');
    redirect(`/${newOrg.slug}`);
  } catch (error) {
    // If database creation fails, clean up Clerk organization
    await clerkClient.organizations.deleteOrganization(clerkOrg.id);
    throw error;
  }
}

export async function updateOrganization(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user is owner or admin
  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (!org.length || org[0].ownerId !== userId) {
    throw new Error('Unauthorized');
  }

  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const category = formData.get('category') as string;

  if (!name) {
    throw new Error('Name is required');
  }

  const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

  await db
    .update(organizations)
    .set({
      name,
      description,
      category,
      slug,
      updatedAt: new Date(),
    })
    .where(eq(organizations.id, organizationId));

  // Update Clerk organization
  await clerkClient.organizations.updateOrganization(org[0].clerkOrgId, {
    name,
  });

  revalidatePath('/organizations');
  revalidatePath(`/${slug}`);
}

export async function deleteOrganization(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, organizationId))
    .limit(1);

  if (!org.length || org[0].ownerId !== userId) {
    throw new Error('Unauthorized');
  }

  // Delete Clerk organization
  await clerkClient.organizations.deleteOrganization(org[0].clerkOrgId);

  // Delete from database (cascade will handle related records)
  await db
    .delete(organizations)
    .where(eq(organizations.id, organizationId));

  revalidatePath('/organizations');
  redirect('/organizations');
}

export async function getOrganizations(userId: string) {
  return await db
    .select()
    .from(organizations)
    .where(eq(organizations.ownerId, userId));
}

export async function getOrganizationBySlug(slug: string) {
  const result = await db
    .select()
    .from(organizations)
    .where(eq(organizations.slug, slug))
    .limit(1);

  return result[0] || null;
}

export async function getUserOrganizations(userId: string) {
  // Get organizations where user is owner or team member
  const ownedOrgs = await db
    .select()
    .from(organizations)
    .where(eq(organizations.ownerId, userId));

  const teamOrgs = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      slug: organizations.slug,
      image: organizations.image,
      category: organizations.category,
      role: teams.role,
    })
    .from(teams)
    .innerJoin(organizations, eq(teams.organizationId, organizations.id))
    .where(and(eq(teams.userId, userId), eq(teams.isActive, true)));

  return {
    owned: ownedOrgs,
    member: teamOrgs,
  };
}
