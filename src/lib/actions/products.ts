'use server';

import { db } from '@/db';
import { organizations, teams, products, productCourses, courses } from '@/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

async function checkOrganizationAccess(organizationId: string, userId: string) {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) return true;

  // Check if user is team member with editor or admin role
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  return teamMember.length > 0;
}

export async function createProduct(organizationId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const price = parseFloat(formData.get('price') as string);
  const paymentType = formData.get('paymentType') as 'one_time' | 'monthly' | 'annually';
  const accessDurationMonths = formData.get('accessDurationMonths') ? 
    parseInt(formData.get('accessDurationMonths') as string) : null;
  const courseIds = formData.getAll('courseIds') as string[];

  if (!name || !price || !paymentType) {
    throw new Error('Name, price, and payment type are required');
  }

  if (price <= 0) {
    throw new Error('Price must be greater than 0');
  }

  try {
    // Create product
    const [newProduct] = await db
      .insert(products)
      .values({
        organizationId,
        name,
        description,
        price: price.toString(),
        paymentType,
        accessDurationMonths,
      })
      .returning();

    // Link courses to product if provided
    if (courseIds.length > 0) {
      const productCourseData = courseIds.map((courseId) => ({
        productId: newProduct.id,
        courseId,
      }));

      await db.insert(productCourses).values(productCourseData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/products`);
    redirect(`/${org[0].slug}/products`);
  } catch (error) {
    console.error('Error creating product:', error);
    throw new Error('Failed to create product');
  }
}

export async function updateProduct(productId: string, formData: FormData) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get product and check access
  const product = await db
    .select()
    .from(products)
    .where(eq(products.id, productId))
    .limit(1);

  if (!product.length) {
    throw new Error('Product not found');
  }

  const hasAccess = await checkOrganizationAccess(product[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  const name = formData.get('name') as string;
  const description = formData.get('description') as string;
  const price = parseFloat(formData.get('price') as string);
  const paymentType = formData.get('paymentType') as 'one_time' | 'monthly' | 'annually';
  const accessDurationMonths = formData.get('accessDurationMonths') ? 
    parseInt(formData.get('accessDurationMonths') as string) : null;
  const isActive = formData.get('isActive') === 'true';
  const courseIds = formData.getAll('courseIds') as string[];

  if (!name || !price || !paymentType) {
    throw new Error('Name, price, and payment type are required');
  }

  if (price <= 0) {
    throw new Error('Price must be greater than 0');
  }

  try {
    // Update product
    await db
      .update(products)
      .set({
        name,
        description,
        price: price.toString(),
        paymentType,
        accessDurationMonths,
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(products.id, productId));

    // Update product courses
    // First, remove existing associations
    await db
      .delete(productCourses)
      .where(eq(productCourses.productId, productId));

    // Then add new associations
    if (courseIds.length > 0) {
      const productCourseData = courseIds.map((courseId) => ({
        productId,
        courseId,
      }));

      await db.insert(productCourses).values(productCourseData);
    }

    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, product[0].organizationId))
      .limit(1);

    revalidatePath(`/${org[0].slug}/products`);
  } catch (error) {
    console.error('Error updating product:', error);
    throw new Error('Failed to update product');
  }
}

export async function deleteProduct(productId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get product and check access
  const product = await db
    .select()
    .from(products)
    .where(eq(products.id, productId))
    .limit(1);

  if (!product.length) {
    throw new Error('Product not found');
  }

  const hasAccess = await checkOrganizationAccess(product[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Delete product (cascade will handle related records)
  await db
    .delete(products)
    .where(eq(products.id, productId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, product[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/products`);
}

export async function getProducts(organizationId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  return await db
    .select()
    .from(products)
    .where(eq(products.organizationId, organizationId));
}

export async function getProductWithCourses(productId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get product
  const product = await db
    .select()
    .from(products)
    .where(eq(products.id, productId))
    .limit(1);

  if (!product.length) {
    return null;
  }

  const hasAccess = await checkOrganizationAccess(product[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get product courses
  const productCoursesData = await db
    .select({
      course: courses,
    })
    .from(productCourses)
    .innerJoin(courses, eq(productCourses.courseId, courses.id))
    .where(eq(productCourses.productId, productId));

  return {
    ...product[0],
    courses: productCoursesData.map(pc => pc.course),
  };
}

export async function getAvailableCoursesForProduct(organizationId: string, productId?: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  const hasAccess = await checkOrganizationAccess(organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Get all courses for the organization
  const allCourses = await db
    .select()
    .from(courses)
    .where(eq(courses.organizationId, organizationId));

  return allCourses;
}

export async function toggleProductStatus(productId: string) {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error('Unauthorized');
  }

  // Get product and check access
  const product = await db
    .select()
    .from(products)
    .where(eq(products.id, productId))
    .limit(1);

  if (!product.length) {
    throw new Error('Product not found');
  }

  const hasAccess = await checkOrganizationAccess(product[0].organizationId, userId);
  if (!hasAccess) {
    throw new Error('Unauthorized');
  }

  // Toggle active status
  await db
    .update(products)
    .set({
      isActive: !product[0].isActive,
      updatedAt: new Date(),
    })
    .where(eq(products.id, productId));

  const org = await db
    .select()
    .from(organizations)
    .where(eq(organizations.id, product[0].organizationId))
    .limit(1);

  revalidatePath(`/${org[0].slug}/products`);
}
