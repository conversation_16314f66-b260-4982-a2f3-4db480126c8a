import { db } from '@/db';
import { organizations, teams } from '@/schema';
import { eq, and } from 'drizzle-orm';

/**
 * Check if a user has access to an organization
 * Returns the user's role in the organization or null if no access
 */
export async function checkOrganizationAccess(
  organizationId: string, 
  userId: string
): Promise<{ hasAccess: boolean; role: 'admin' | 'editor' | null }> {
  // Check if user is owner
  const org = await db
    .select()
    .from(organizations)
    .where(and(
      eq(organizations.id, organizationId),
      eq(organizations.ownerId, userId)
    ))
    .limit(1);

  if (org.length) {
    return { hasAccess: true, role: 'admin' };
  }

  // Check if user is team member
  const teamMember = await db
    .select()
    .from(teams)
    .where(and(
      eq(teams.organizationId, organizationId),
      eq(teams.userId, userId),
      eq(teams.isActive, true)
    ))
    .limit(1);

  if (teamMember.length) {
    return { hasAccess: true, role: teamMember[0].role };
  }

  return { hasAccess: false, role: null };
}

/**
 * Generate a URL-friendly slug from a string
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

/**
 * Check if a user has reached their organization limit based on their plan
 */
export async function checkOrganizationLimit(
  userId: string, 
  planType: 'basic' | 'professional' | 'pro' | 'business'
): Promise<{ canCreate: boolean; currentCount: number; limit: number }> {
  const limits = {
    basic: 1,
    professional: 3,
    pro: 10,
    business: 30,
  };

  const currentOrgs = await db
    .select()
    .from(organizations)
    .where(eq(organizations.ownerId, userId));

  const limit = limits[planType];
  const currentCount = currentOrgs.length;

  return {
    canCreate: currentCount < limit,
    currentCount,
    limit,
  };
}

/**
 * Get organization by slug with error handling
 */
export async function getOrganizationBySlug(slug: string) {
  const result = await db
    .select()
    .from(organizations)
    .where(eq(organizations.slug, slug))
    .limit(1);

  return result[0] || null;
}

/**
 * Validate that a user can perform admin actions on an organization
 */
export async function validateAdminAccess(
  organizationId: string, 
  userId: string
): Promise<boolean> {
  const access = await checkOrganizationAccess(organizationId, userId);
  return access.hasAccess && access.role === 'admin';
}

/**
 * Validate that a user can perform editor actions on an organization
 */
export async function validateEditorAccess(
  organizationId: string, 
  userId: string
): Promise<boolean> {
  const access = await checkOrganizationAccess(organizationId, userId);
  return access.hasAccess && (access.role === 'admin' || access.role === 'editor');
}

/**
 * Common error messages
 */
export const ErrorMessages = {
  UNAUTHORIZED: 'Unauthorized',
  NOT_FOUND: 'Resource not found',
  INVALID_INPUT: 'Invalid input provided',
  PLAN_LIMIT_REACHED: 'Plan limit reached',
  ALREADY_EXISTS: 'Resource already exists',
  INTERNAL_ERROR: 'Internal server error',
} as const;

/**
 * Common success messages
 */
export const SuccessMessages = {
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
} as const;
