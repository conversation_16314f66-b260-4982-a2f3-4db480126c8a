import { useTranslations } from "next-intl";
import { Label } from "../ui/label";

export default function ImageUploader({ label }: { label: string }) {
  const tf = useTranslations("InApp.forms");

  return (
    <>
      <div className="flex flex-col justify-center w-full ">
        <Label className="text-xs mb-2 font-semibold">{label}</Label>
        <label
          className="flex flex-col items-center justify-end w-full h-44 border border-gray-200 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 bg-white bg-image-upload bg-no-repeat bg-top-8 bg-35 p-4"
          htmlFor="dropzone-file"
        >
          <div className="flex flex-col items-center justify-end pt-5 pb-6 text-center ">
            <p className="mb-2 text-[10px] font-semibold text-gray-500 dark:text-gray-400 ">
              {tf("dropFiles")}
            </p>
            <p className="text-[10px] text-gray-500 dark:text-gray-400">
              PNG | JPG
            </p>
          </div>
          <input
            accept="image/png, image/jpeg"
            className="hidden"
            id="dropzone-file"
            type="file"
          />
        </label>
      </div>
    </>
  );
}
