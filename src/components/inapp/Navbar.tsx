import { SignedIn, SignedOut, SignIn<PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { buttonVariants } from "@/components/ui/button";
import Link from "next/link";
import { useTranslations } from "next-intl";

type Props = {
  isCollapsed: boolean;
};

export const Navbar = ({ isCollapsed }: Props) => {
  const t = useTranslations("InApp.navbar");

  const collapseStyles = () => {
    if (isCollapsed) {
      return {
        logo: "w-[64px] 2xl:w-[64px] group-hover:w-[220px] 2xl:group-hover:w-[64px]",
      };
    } else {
      return {
        logo: "w-[64px] 2xl:w-[220px] group-hover:w-[220px] 2xl:group-hover:w-[220px]",
      };
    }
  };

  return (
    <section className="w-full h-14 flex justify-between border-b border-gray-200 pr-4 items-center bg-white transition-all">
      <div
        className={`h-14 flex flex-row items-center justify-between pl-4 flex-shrink-0 flex-grow-0 transition-all ${
          collapseStyles().logo
        } ${collapseStyles().logo}`}
      >
        <h1 className="text-sm 2xl:text-lg font-bold ">t-chr</h1>
      </div>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/components">Components</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Breadcrumb</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="relative ">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search..."
          className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"
        />
      </div>
      <nav className="hidden flex-col gap-6 text-lg font-medium md:flex md:flex-row md:items-center md:gap-5 md:text-sm lg:gap-6">
        <SignedOut>
          <Link href="/" className={buttonVariants({ variant: "ghost" })}>
            {t("home")}
          </Link>
          <Link
            href="/pricing"
            className={buttonVariants({ variant: "ghost" })}
          >
            {t("pricing")}
          </Link>
          <Link
            href="/support"
            className={buttonVariants({ variant: "ghost" })}
          >
            {t("support")}
          </Link>
        </SignedOut>

        <SignedIn>
          <div className="flex flex-end gap-4 items-center">
            <UserButton
              afterSignOutUrl="/"
              userProfileUrl="/account"
              userProfileMode="navigation"
              appearance={{
                elements: {
                  userButtonPopoverCard: "border-gray-200",
                  userButtonPopoverFooter: "hidden",
                },
              }}
            />
          </div>
        </SignedIn>
      </nav>
    </section>
  );
};

export default Navbar;
