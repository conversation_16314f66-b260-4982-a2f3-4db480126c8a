"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Toolbar from "./Toolbar";

const TipTap = ({
  description,
  onChange,
}: {
  description: string;
  onChange: (richText: string) => void;
}) => {
  const editor = useEditor({
    extensions: [StarterKit],
    content: description,
    editorProps: {
      attributes: {
        class:
          "rounded-md mt-2 border min-h-[150px] border-input focus:outline-none focus:ring ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 p-4 max-h-[680px] overflow-y-auto",
      },
    },
    onUpdate: ({ editor }) => {
      const richText = editor.getHTML();
      onChange(richText);
    },
  });

  return (
    <div className="prose max-w-full">
      <Toolbar editor={editor} />
      <EditorContent editor={editor} className="bg-white" />
    </div>
  );
};

export default TipTap;
