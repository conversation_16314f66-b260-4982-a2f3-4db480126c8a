/**
 * v0 by Vercel.
 * @see https://v0.dev/t/zSx5IRKwwbF
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { useTranslations } from "next-intl";

export default function CourseTabs() {
  const [activeTab, setActiveTab] = useState<string | undefined>("description");
  const t = useTranslations("InApp.pages.lessons");

  return (
    <div className="rounded-lg border border-gray-100 bg-white p-6 dark:border-gray-800 dark:bg-gray-950">
      <Tabs
        defaultValue="description"
        className="mb-6"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="flex justify-start mb-6 bg-transparent px-0">
          <TabsTrigger
            value="description"
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
          >
            {t("description")}
          </TabsTrigger>
          <TabsTrigger
            value="downloads"
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
          >
            {t("materials")}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="description">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">About this product</h3>
            <p className="text-gray-500 dark:text-gray-400">
              This is a beautifully designed and highly functional React
              component library. It includes a wide range of components that are
              easy to use and customize. The library is built with accessibility
              in mind and is fully responsive.
            </p>
            <p className="text-gray-500 dark:text-gray-400">
              The components are designed to be used in a variety of
              applications, from simple websites to complex web applications.
              They are also easy to integrate into existing projects, making it
              easy to add new features and functionality to your application.
            </p>
          </div>
        </TabsContent>
        <TabsContent value="downloads">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Download options</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>ZIP File</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 dark:text-gray-400">
                    Download the entire library as a ZIP file.
                  </p>
                </CardContent>
                <CardFooter>
                  <Link
                    href="#"
                    className="inline-flex h-9 items-center justify-center rounded-md bg-gray-900 px-4 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
                    prefetch={false}
                  >
                    Download
                  </Link>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>NPM Package</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 dark:text-gray-400">
                    Install the library using npm.
                  </p>
                </CardContent>
                <CardFooter>
                  <Link
                    href="#"
                    className="inline-flex h-9 items-center justify-center rounded-md bg-gray-900 px-4 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
                    prefetch={false}
                  >
                    Install
                  </Link>
                </CardFooter>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>GitHub Repository</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 dark:text-gray-400">
                    View the source code on GitHub.
                  </p>
                </CardContent>
                <CardFooter>
                  <Link
                    href="#"
                    className="inline-flex h-9 items-center justify-center rounded-md bg-gray-900 px-4 text-sm font-medium text-gray-50 shadow transition-colors hover:bg-gray-900/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:pointer-events-none disabled:opacity-50 dark:bg-gray-50 dark:text-gray-900 dark:hover:bg-gray-50/90 dark:focus-visible:ring-gray-300"
                    prefetch={false}
                  >
                    View on GitHub
                  </Link>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
