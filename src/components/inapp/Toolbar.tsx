"use client";
import { type Editor } from "@tiptap/react";
import {
  Bold,
  Strikethrough,
  Underline,
  Italic,
  List,
  ListOrdered,
  Heading2,
} from "lucide-react";
import { Toggle } from "@/components/ui/toggle";

type Props = {
  editor: Editor | null;
};

const Toolbar = ({ editor }: Props) => {
  if (!editor) return null;
  return (
    <div className="flex items-center justify-between rounded-md  border border-input p-2 bg-white">
      <div className="flex items-center gap-2">
        <Toggle
          size="sm"
          pressed={editor.isActive("bold")}
          onPressedChange={() => {
            editor.chain().focus().toggleBold().run();
          }}
        >
          <Bold size={16} />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("italic")}
          onPressedChange={() => {
            editor.chain().focus().toggleItalic().run();
          }}
        >
          <Italic size={16} />
        </Toggle>

        <Toggle
          size="sm"
          pressed={editor.isActive("strike")}
          onPressedChange={() => {
            editor.chain().focus().toggleStrike().run();
          }}
        >
          <Strikethrough size={16} />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("bulletList")}
          onPressedChange={() => {
            editor.chain().focus().toggleBulletList().run();
          }}
        >
          <List size={16} />
        </Toggle>
        <Toggle
          size="sm"
          pressed={editor.isActive("orderedList")}
          onPressedChange={() => {
            editor.chain().focus().toggleOrderedList().run();
          }}
        >
          <ListOrdered size={16} />
        </Toggle>
      </div>
    </div>
  );
};

export default Toolbar;
