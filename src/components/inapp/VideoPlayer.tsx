"use client";

import ReactPlayer from "react-player/lazy";

export type Props = {
  url: string;
  title?: string;
  thumbnail?: string;
  cover?: string;
};

const VideoPlayer = ({ url }: Props) => {
  return (
    <div className="player-wrapper rounded-xl overflow-hidden">
      <ReactPlayer
        url={url}
        width="100%"
        height="100%"
        controls
        playing
        light
        muted
        className="react-player"
      />
    </div>
  );
};

export default VideoPlayer;
