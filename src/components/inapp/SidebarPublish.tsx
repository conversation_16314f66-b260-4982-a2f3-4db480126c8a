import { ExternalLink, BookDashed } from "lucide-react";
import { useTranslations } from "next-intl";
import { Button } from "../ui/button";
import ImageUploader from "./ImageUploader";
import FileUploader from "./FileUploader";

const SidebarPublish = ({ type }: { type: string }) => {
  const t = useTranslations("InApp.sidebar.publish");
  const tf = useTranslations("InApp.forms");
  const tp = useTranslations("InApp.pages.new");

  return (
    <section className="w-full h-full border-l border-gray-200 bg-white z-10 relative">
      <div className="w-full h-full overflow-y-scroll scrollbar-hide pb-24">
        <div className="w-full flex flex-col gap-1 p-4">
          <h3 className="uppercase text-xs border-l-2 border-gray-200 pl-1 mb-4">
            {t("widgetPublish.publish")}
          </h3>
          <p className="text-xs">
            <span className="font-semibold">{tf("courseStatus")}</span> Draft
          </p>
          <p className="text-xs">
            <span className="font-semibold">{tf("createdAt")}</span> 31/07/2023
          </p>
          <div className="flex flex-row items-center gap-2 mt-2">
            <Button size="sm">
              {t("widgetPublish.saveDraft")}
              <BookDashed size={16} className="ml-2" />
            </Button>
            <Button size="sm">
              {t("widgetPublish.title")}{" "}
              <ExternalLink size={16} className="ml-2" />
            </Button>
          </div>
        </div>

        {type && type === "course" ? (
          <div className="w-full flex flex-col gap-1 p-4">
            <h3 className="uppercase text-xs border-l-2 border-gray-200 pl-1 mb-4">
              {t("widgetImages.title")}
            </h3>
            <div className="flex flex-col gap-4">
              <ImageUploader label={t("widgetImages.thumbnail")} />
              <ImageUploader label={t("widgetImages.cover")} />
            </div>
          </div>
        ) : (
          ""
        )}

        {type && type === "lesson" ? (
          <div className="w-full flex flex-col gap-1 p-4">
            <h3 className="uppercase text-xs border-l-2 border-gray-200 pl-1 mb-4">
              {tp("materials")}
            </h3>
            <div className="flex flex-col gap-4">
              <FileUploader label={tp("materials")} />
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
    </section>
  );
};
export default SidebarPublish;
