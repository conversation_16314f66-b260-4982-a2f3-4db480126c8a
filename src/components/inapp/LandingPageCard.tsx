import { BookA, Building, ExternalLink, Package, Users } from "lucide-react";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Button } from "../ui/button";

export type Landing = {
  id: string;
  title: string;
  image: string;
  category: string;
  product: string;
  organization: string;
  slug: string;
};

export const LandingPageCard = ({ item }: { item: Landing }) => {
  const t = useTranslations("InApp.pages.organizations");

  return (
    <Card className="w-full max-w-sm relative">
      <Button
        className="absolute top-4 right-4"
        variant="outline"
        size="icon"
        asChild
      >
        <Link href="/my-landing-page-link">
          <ExternalLink className="h-4 w-4" />
        </Link>
      </Button>
      <div className="aspect-w-5 aspect-h-4">
        <Link href={`/landing-pages/${item.slug}`}>
          <Image
            alt="Product"
            className="object-cover rounded-t-lg"
            src="/placeholder.svg"
            style={{
              aspectRatio: "500/400",
              objectFit: "cover",
            }}
            width={500}
            height={400}
          />
        </Link>
      </div>
      <CardHeader className="flex flex-col gap-1 pt-4 pb-2 px-4">
        <CardTitle>{item.title ? item.title : "Course Title"}</CardTitle>
        <CardDescription>
          {item.title ? item.title : "Category"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2 pb-4 px-4">
        <ul className="flex flex-col gap-2">
          <li className="flex items-center gap-2 text-sm">
            <Package size={12} />
            <strong>
              {item && item.product && item.product ? item.product : ""}
            </strong>
          </li>
          <li className="flex items-center gap-2 text-sm">
            <Building size={12} />
            <strong>
              {item && item.organization && item.organization
                ? item.organization
                : "7"}
            </strong>
          </li>
        </ul>
      </CardContent>
    </Card>
  );
};
