/**
 * v0 by Vercel.
 * @see https://v0.dev/t/7ZHOD8BtQ3T
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
"use client";

import { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import Link from "next/link";
import { Circle, CircleCheck, FileIcon } from "lucide-react";

export default function ModulesList() {
  const [activeLesson, setActiveLesson] = useState<string | null>(null);

  return (
    <div className=" rounded-xl ">
      <div className="space-y-4">
        <Accordion type="single" collapsible>
          <AccordionItem
            value="module-1"
            className="bg-white dark:bg-gray-800 px-4 mb-4 rounded-xl border border-gray-100 dark:border-gray-700"
          >
            <AccordionTrigger className="flex items-center justify-between hover:no-underline">
              <span className="font-medium">Module 1: Introduction</span>
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid gap-2 pt-2">
                <Link
                  href="#"
                  className={`flex items-center gap-2 rounded-md px-3 py-2 transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 ${
                    activeLesson === "lesson-1"
                      ? "bg-gray-200 dark:bg-gray-700"
                      : ""
                  }`}
                  onClick={() => setActiveLesson("lesson-1")}
                  prefetch={false}
                >
                  <CircleCheck className="w-4 h-4" />
                  <span>Lesson 1: Welcome to the Course</span>
                </Link>
                <Link
                  href="#"
                  className={`flex items-center gap-2 rounded-md px-3 py-2 transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 ${
                    activeLesson === "lesson-2"
                      ? "bg-gray-200 dark:bg-gray-700"
                      : ""
                  }`}
                  onClick={() => setActiveLesson("lesson-2")}
                  prefetch={false}
                >
                  <Circle className="w-4 h-4" />
                  <span>Lesson 2: Course Overview</span>
                </Link>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
