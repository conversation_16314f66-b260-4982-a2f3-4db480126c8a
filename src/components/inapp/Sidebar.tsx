import {
  BookA,
  LibraryBig,
  BookOpenText,
  FileBadge,
  Users,
  LayoutTemplate,
  Package,
  TicketPercent,
  Receipt,
  GraduationCap,
  CircleUser,
  Sparkles,
  Building,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import Link from "next/link";

export type Props = {
  isCollapsed: boolean;
};

const Sidebar = ({ isCollapsed }: Props) => {
  const t = useTranslations("InApp.sidebar");
  const params = useParams<{ locale: string; organization: string }>();

  const collapseStyles = () => {
    if (isCollapsed) {
      return {
        wrapper:
          "w-[64px] 2xl:w-[64px] group-hover:w-[220px] 2xl:group-hover:w-[64px]",
        nav: "relative px-2 py-2 2xl:px-2 2xl:py-2 group-hover:p-2 2xl:group-hover:px-2 2xl:group-hover:py-2 after:content[''] after:absolute after:left-[10%] after:-bottom-[16px] after:block group-hover:after:hidden 2xl:group-hover:after:block after:h-[1px] after:bg-gray-200 after:w-[80%] after:transition-all 2xl:after:transition-all",
        link: "justify-center 2xl:justify-center px-2 2xl:p-2 group-hover:justify-start group-hover:px-3  2xl:group-hover:justify-center 2xl:group-hover:p-2 transition-all duration-300 ",
        title:
          "opacity-0 group-hover:opacity-100 2xl:opacity-0 2xl:group-hover:opacity-0 text-3xs 2xl:text-3xs group-hover:text-xs 2xl:group-hover:text-3xs transition-all duration-300 ",
        icon: "mr-0 2xl:mr-0 group-hover:mr-2 2xl:group-hover:mr-0",
        label:
          "opacity-0 w-0 group-hover:opacity-100 group-hover:w-full 2xl:group-hover:opacity-0 2xl:group-hover:w-0 ",
      };
    } else {
      return {
        wrapper:
          "w-[64px] 2xl:w-[220px] group-hover:w-[220px] 2xl:group-hover:w-[220px]",
        nav: "relative px-2 py-2 2xl:p-2 group-hover:p-2 group-hover:after:hidden after:content[''] after:absolute after:left-[10%] after:-bottom-[16px] after:block 2xl:after:hidden afterafter:h-[1px] after:bg-gray-200 after:w-[80%] after:transition-all 2xl:after:transition-all",
        link: "justify-center 2xl:justify-start px-2 py-2 2xl:px-3 group-hover:justify-start group-hover:px-3 transition-all ",
        title:
          "opacity-0 group-hover:opacity-100 2xl:opacity-100 text-3xs group-hover:text-xs text-3xs 2xl:text-xs transition-all ",
        icon: "mr-0 2xl:mr-2 group-hover:mr-2",
        label:
          "w-0 opacity-0 2xl:opacity-100 group-hover:opacity-100 2xl:w-full group-hover:w-full transition-all duration-300 ",
      };
    }
  };

  return (
    <section className={`flex flex-col h-full ${collapseStyles().wrapper}`}>
      <div className="w-full h-[calc(100%-64px)] overflow-y-scroll scrollbar-hide pb-24">
        <nav className={`flex flex-col gap-1  ${collapseStyles().nav} `}>
          <h3
            className={`h-5 uppercase ${
              collapseStyles().title
            } border-l-2 border-gray-200 pl-1`}
          >
            {t("studio.sectionTitle")}
          </h3>

          <Link
            href="/studio/ai-creator"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <Sparkles className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("studio.menuAI.aiCreator")}
            </span>
          </Link>
          <Link
            href={`/landing-pages`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <LayoutTemplate className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuLandingPages.landingPages")}
            </span>
          </Link>
        </nav>

        <nav className={`flex flex-col gap-1  ${collapseStyles().nav} `}>
          <h3
            className={`h-5 uppercase ${
              collapseStyles().title
            } border-l-2 border-gray-200 pl-1`}
          >
            {t("courses.sectionTitle")}
          </h3>

          <Link
            href={`/${params.organization}/courses`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <BookA className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuCourses.courses")}
            </span>
          </Link>
          <Link
            href={`/${params.organization}/modules`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <LibraryBig className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuModules.modules")}
            </span>
          </Link>

          <Link
            href={`/${params.organization}/lessons`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <BookOpenText className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuLessons.lessons")}
            </span>
          </Link>
          <Link
            href={`/${params.organization}/subscribers`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <GraduationCap className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuSubscribers.subscribers")}
            </span>
          </Link>
          <Link
            href={`/${params.organization}/certificates`}
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <FileBadge className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("courses.menuCertificates.certificates")}
            </span>
          </Link>
        </nav>

        <nav className={`flex flex-col gap-1  ${collapseStyles().nav} `}>
          <h3
            className={`h-5 uppercase ${
              collapseStyles().title
            } border-l-2 border-gray-200 pl-1`}
          >
            {t("products.sectionTitle")}
          </h3>

          <Link
            href="/products"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <Package className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("products.menuProducts.products")}
            </span>
          </Link>
          <Link
            href="/coupons"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <TicketPercent className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("products.menuCoupons.coupons")}
            </span>
          </Link>

          <Link
            href="#"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <Receipt className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("products.menuRevenue.revenue")}
            </span>
          </Link>
        </nav>

        <nav className={`flex flex-col gap-1  ${collapseStyles().nav} `}>
          <h3
            className={`h-5 uppercase ${
              collapseStyles().title
            } border-l-2 border-gray-200 pl-1`}
          >
            {t("organizations.sectionTitle")}
          </h3>

          <Link
            href="/organizations"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <Building className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("organizations.menuOrganizations.organizations")}
            </span>
          </Link>
          <Link
            href="/teams"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <Users className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("account.menuTeams.teams")}
            </span>
          </Link>
        </nav>

        <nav className={`flex flex-col gap-1  ${collapseStyles().nav} `}>
          <h3
            className={`h-5 uppercase ${
              collapseStyles().title
            } border-l-2 border-gray-200 pl-1`}
          >
            {t("account.sectionTitle")}
          </h3>

          <Link
            href="/account"
            className={`flex h-8 items-center ${
              collapseStyles().link
            }  text-sm rounded-md hover:bg-gray-50`}
          >
            <CircleUser className={`w-4 h-4 ${collapseStyles().icon} `} />
            <span className={`${collapseStyles().label} `}>
              {t("account.menuAccount.myAccount")}
            </span>
          </Link>
        </nav>
      </div>
    </section>
  );
};
export default Sidebar;
