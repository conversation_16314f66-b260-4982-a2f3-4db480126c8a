import { BookA, Users } from "lucide-react";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import Link from "next/link";

export type Organization = {
  id: string;
  title: string;
  image: string;
  category: string;
  courses: { count: number };
  subscribers: { count: number };
  slug: string;
};

export const OrganizationCard = ({ item }: { item: Organization }) => {
  const t = useTranslations("InApp.pages.organizations");

  return (
    <Card className="w-full max-w-sm">
      <div className="aspect-w-5 aspect-h-4">
        <Link href={`/organizations/${item.slug}`}>
          <Image
            alt="Product"
            className="object-cover rounded-t-lg"
            src="/placeholder.svg"
            style={{
              aspectRatio: "500/400",
              objectFit: "cover",
            }}
            width={500}
            height={400}
          />
        </Link>
      </div>
      <CardHeader className="flex flex-col gap-1 pt-4 pb-2 px-4">
        <CardTitle>{item.title ? item.title : "Course Title"}</CardTitle>
        <CardDescription>
          {item.title ? item.title : "Category"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2 pb-4 px-4">
        <ul className="flex flex-col gap-2">
          <li className="flex items-center gap-2 text-sm">
            <BookA size={12} />
            <strong>
              {item && item.courses && item.courses.count
                ? item.courses.count
                : ""}
            </strong>{" "}
            {t("courses")}
          </li>
          <li className="flex items-center gap-2 text-sm">
            <Users size={12} />
            <strong>
              {item && item.subscribers && item.subscribers.count
                ? item.subscribers.count
                : "7"}
            </strong>{" "}
            {t("subscribers")}
          </li>
        </ul>
      </CardContent>
    </Card>
  );
};
