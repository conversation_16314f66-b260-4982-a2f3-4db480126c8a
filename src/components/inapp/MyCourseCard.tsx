import {
  BookA,
  LibraryBig,
  BookOpenText,
  FileBadge,
  Users,
  LayoutTemplate,
  Package,
  TicketPercent,
  Receipt,
  GraduationCap,
  CircleUser,
  Sparkles,
  CirclePlus,
  Clock,
} from "lucide-react";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Progress } from "../ui/progress";

export type Course = {
  id: string;
  title: string;
  image: string;
  category: string;
  modules: Array<string>;
  lessons: Array<string>;
  duration: string;
  slug: string;
  progress: number;
};

export const MyCourseCard = ({ item }: { item: Course }) => {
  const t = useTranslations("InApp.pages.courses");

  return (
    <Card className="w-full max-w-sm">
      <div className="aspect-w-5 aspect-h-4">
        <Link href={`/organizations/${item.slug}`}>
          <Image
            alt="Product"
            className="object-cover rounded-t-lg"
            src="/placeholder.svg"
            style={{
              aspectRatio: "500/400",
              objectFit: "cover",
            }}
            width={500}
            height={400}
          />
        </Link>
      </div>
      <CardHeader className="flex flex-col gap-1 pt-4 pb-2 px-4">
        <CardTitle>{item.title ? item.title : "Course Title"}</CardTitle>
        <CardDescription>
          {item.title ? item.title : "Category"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2 pb-4 px-4">
        <ul className="flex flex-col gap-2">
          <li className="flex items-center gap-2 text-sm">
            <LibraryBig size={12} />
            <strong>{item.modules ? item.modules.length : "8"}</strong>{" "}
            {t("modules")}
          </li>
          <li className="flex items-center gap-2 text-sm">
            <BookOpenText size={12} />
            <strong>{item.lessons ? item.lessons.length : "124"}</strong>{" "}
            {t("lessons")}
          </li>
          <li className="flex items-center gap-2 text-sm">
            <Clock size={12} />
            <strong>{item.duration ? item.duration : "7"}h</strong>{" "}
            {t("duration")}
          </li>
          <li className="mt-2 flex-col gap-2">
            <div className="w-full flex items-center gap-2 text-sm relative h-4">
              <small
                className={`block w-8 h-8 text-gray-500 absolute left-[${item.progress}%] top-0`}
              >
                {item.progress}%
              </small>
            </div>
            <Progress value={item.progress} />
          </li>
        </ul>
      </CardContent>
    </Card>
  );
};
