"use client";
import { useCallback } from "react";
import { useNode } from "@craftjs/core";
import ContentEditable from "react-contenteditable";
import { Slider } from "@/components/ui/slider";
import { Label } from "@radix-ui/react-label";
import { FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export type TextProps = {
  text: string;
  fontSize: number;
  textAlign: string;
  className?: string;
};
export type SliderProps = React.ComponentProps<typeof Slider>;

export const Text = ({ className, text, fontSize, textAlign }: TextProps) => {
  const {
    connectors: { connect, drag },
    actions: { setProp },
  } = useNode();

  return (
    <div ref={(ref: any) => connect(drag(ref!))}>
      <ContentEditable
        html={text}
        onChange={(e) =>
          setProp(
            (props: any) =>
              (props.text = e.target.value.replace(/<\/?[^>]+(>|$)/g, ""))
          )
        }
        tagName="p"
        style={{ fontSize: `${fontSize}px`, textAlign }}
      />
    </div>
  );
};

const TextSettings = ({ className, ...props }: SliderProps) => {
  const {
    actions: { setProp },
    fontSize,
  } = useNode((node) => ({
    fontSize: node.data.props.fontSize,
  }));

  return (
    <div className="flex flex-col gap-1">
      <label className="text-2xs font-medium">Font Size</label>
      <Input
        type="number"
        value={fontSize}
        onChange={(e) =>
          setProp((props: any) => (props.fontSize = e.target.value))
        }
        className="w-20"
      />
    </div>
  );
};

Text.craft = {
  props: {
    text: "Hello",
    fontSize: 16,
  },
  rules: {
    canDrag: (node: any) => node.data.props.text != "Drag",
  },
  related: {
    settings: TextSettings,
  },
};
