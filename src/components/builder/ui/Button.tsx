"use client";
import { useNode } from "@craftjs/core";
import { Button as ShadcnButton } from "../../ui/button";

export type ButtonProps = {
  size?: "default" | "sm" | "lg" | "icon" | null | undefined;
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | null
    | undefined;
  className?: string | undefined;
  children: React.ReactNode;
};
export const Button = ({ size, variant, className, children }: ButtonProps) => {
  const {
    connectors: { connect, drag },
  } = useNode();
  return (
    <ShadcnButton
      ref={(ref: any) => connect(drag(ref!))}
      size={size}
      variant={variant}
      className={className}
    >
      {children}
    </ShadcnButton>
  );
};
