import { LayoutTemplate, Cuboid, Component, PanelTop } from "lucide-react";
import { useTranslations } from "next-intl";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Primitives } from "./Primitives";

export const MenuItems = () => {
  const t = useTranslations("InApp.builder");

  const items = [
    {
      id: "pages",
      title: t("pages"),
      icon: <PanelTop className="w-4 h-4" />,
    },
    {
      id: "templates",
      title: t("templates"),
      icon: <LayoutTemplate className="w-4 h-4" />,
    },
    {
      id: "blocks",
      title: t("blocks"),
      icon: <Cuboid className="w-4 h-4" />,
    },
    {
      id: "components",
      title: t("components"),
      icon: <Component className="w-4 h-4" />,
    },
  ];

  const menuItems = items.map((item) => {
    return (
      <Tooltip key={item.id}>
        <TooltipTrigger className="flex justify-center items-center border border-slate-200 rounded-md hover:bg-slate-100 w-8 h-8">
          {item.icon}
        </TooltipTrigger>
        <TooltipContent side="right">{item.title}</TooltipContent>
      </Tooltip>
    );
  });

  return menuItems;
};

export const SidebarBuilderLeft = () => {
  const t = useTranslations("InApp.builder");

  return (
    <aside className="flex w-[280px] bg-white">
      <div className="w-[56px] overflow-y-scroll scrollbar-hide pb-24 border-r border-gray-200">
        <div
          defaultValue="account"
          className="w-[56px] flex flex-col gap-2 items-center py-4"
        >
          <TooltipProvider delayDuration={0}>
            <MenuItems />
          </TooltipProvider>
        </div>
      </div>
      <div className="w-[224px] bg-white p-4 h-screen border-r border-gray-200">
        <Primitives />
      </div>
    </aside>
  );
};

export default SidebarBuilderLeft;
