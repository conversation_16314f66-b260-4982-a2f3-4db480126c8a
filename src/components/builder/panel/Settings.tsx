"use client";
import { useEditor } from "@craftjs/core";
import { useTranslations } from "next-intl";
import React from "react";

export type SliderProps = React.ComponentProps<"input">;

export const Settings = ({ className, ...props }: SliderProps) => {
  const t = useTranslations("InApp.builder");

  const { selected } = useEditor((state) => {
    const currentNodeId = Array.from(state.events.selected)[0];
    let selected;

    if (currentNodeId) {
      selected = {
        id: currentNodeId,
        name: state.nodes[currentNodeId].data.name,
        settings:
          state.nodes[currentNodeId].related &&
          state.nodes[currentNodeId].related.settings,
      };
    }

    return {
      selected,
    };
  });

  return (
    <div className="flex flex-col gap-4 w-[220px] bg-white border-l border-gray-200 right-0 min-h-screen p-4 fixed overflow-hidden">
      {selected && selected.settings && (
        <>
          <h3>
            {selected.name} {t("settings")}
          </h3>
          <div className="flex flex-col gap-4">
            {React.createElement(selected.settings)}
          </div>
        </>
      )}
    </div>
  );
};
