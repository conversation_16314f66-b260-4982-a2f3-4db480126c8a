import { RectangleHorizontal, Square, Text } from "lucide-react";
import { Element, useEditor } from "@craftjs/core";
import { Container } from "../ui/Container";
import { Button } from "../ui/Button";
import { Text as TextComp } from "../ui/Text";

export const Primitives = () => {
  const { connectors, query } = useEditor();

  return (
    <div className="grid grid-cols-3 gap-2">
      <div
        ref={(ref: any) =>
          connectors.create(
            ref,
            <Element is={Container} canvas>
              <Container>Test</Container>
            </Element>
          )
        }
        className="w-14 h-14 flex flex-col gap-1 items-center justify-center rounded-md border border-slate-200 hover:bg-slate-100"
      >
        <Square className="w-6 h-6" />
        <p className="text-3xs">Container</p>
      </div>
      <div
        ref={(ref: any) =>
          connectors.create(ref, <Button size="lg">My Button</Button>)
        }
        className="w-14 h-14 flex flex-col gap-1 items-center justify-center rounded-md border border-slate-200 hover:bg-slate-100"
      >
        <RectangleHorizontal className="w-6 h-6" />
        <p className="text-3xs">Button</p>
      </div>
      <div
        ref={(ref: any) =>
          connectors.create(
            ref,
            <TextComp text="Click me" fontSize={18} textAlign="left" />
          )
        }
        className="w-14 h-14 flex flex-col gap-1 items-center justify-center rounded-md border border-slate-200 hover:bg-slate-100"
      >
        <Text className="w-6 h-6" />
        <p className="text-3xs">Text</p>
      </div>
    </div>
  );
};
