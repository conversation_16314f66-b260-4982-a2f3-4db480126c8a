import {
  integer,
  text,
  boolean,
  pgTable,
  varchar,
  timestamp,
  decimal,
  pgEnum,
  uuid,
  serial,
  jsonb
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

// Enums
export const planTypeEnum = pgEnum('plan_type', ['basic', 'professional', 'pro', 'business']);
export const paymentTypeEnum = pgEnum('payment_type', ['one_time', 'monthly', 'annually']);
export const roleEnum = pgEnum('role', ['admin', 'editor']);
export const lessonTypeEnum = pgEnum('lesson_type', ['video', 'text', 'quiz']);
export const subscriptionStatusEnum = pgEnum('subscription_status', ['active', 'expired', 'cancelled']);

// Organizations table
export const organizations = pgTable("organizations", {
  id: uuid("id").primaryKey().defaultRandom(),
  clerkOrgId: varchar("clerk_org_id", { length: 255 }).notNull().unique(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  slug: varchar("slug", { length: 255 }).notNull().unique(),
  image: text("image"),
  category: varchar("category", { length: 100 }),
  ownerId: varchar("owner_id", { length: 255 }).notNull(), // Clerk user ID
  planType: planTypeEnum("plan_type").notNull().default('basic'),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Teams table (organization members)
export const teams = pgTable("teams", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  userId: varchar("user_id", { length: 255 }).notNull(), // Clerk user ID
  role: roleEnum("role").notNull().default('editor'),
  invitedBy: varchar("invited_by", { length: 255 }).notNull(), // Clerk user ID
  joinedAt: timestamp("joined_at").defaultNow().notNull(),
  isActive: boolean("is_active").default(true).notNull(),
});

// Products table
export const products = pgTable("products", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  paymentType: paymentTypeEnum("payment_type").notNull(),
  accessDurationMonths: integer("access_duration_months"), // null for forever access
  isActive: boolean("is_active").default(true).notNull(),
  stripeProductId: varchar("stripe_product_id", { length: 255 }),
  stripePriceId: varchar("stripe_price_id", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Courses table
export const courses = pgTable("courses", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  category: varchar("category", { length: 100 }),
  image: text("image"),
  slug: varchar("slug", { length: 255 }).notNull(),
  duration: varchar("duration", { length: 50 }), // e.g., "4:30"
  isPublished: boolean("is_published").default(false).notNull(),
  sortOrder: integer("sort_order").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Modules table
export const modules = pgTable("modules", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  sortOrder: integer("sort_order").default(0),
  isPublished: boolean("is_published").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Lessons table
export const lessons = pgTable("lessons", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  content: text("content"), // Rich text content
  videoUrl: text("video_url"),
  lessonType: lessonTypeEnum("lesson_type").notNull().default('video'),
  duration: varchar("duration", { length: 50 }), // e.g., "15:30"
  sortOrder: integer("sort_order").default(0),
  isPublished: boolean("is_published").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Junction table for product-course relationships (many-to-many)
export const productCourses = pgTable("product_courses", {
  id: uuid("id").primaryKey().defaultRandom(),
  productId: uuid("product_id").notNull().references(() => products.id, { onDelete: 'cascade' }),
  courseId: uuid("course_id").notNull().references(() => courses.id, { onDelete: 'cascade' }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Junction table for course-module relationships (many-to-many)
export const courseModules = pgTable("course_modules", {
  id: uuid("id").primaryKey().defaultRandom(),
  courseId: uuid("course_id").notNull().references(() => courses.id, { onDelete: 'cascade' }),
  moduleId: uuid("module_id").notNull().references(() => modules.id, { onDelete: 'cascade' }),
  sortOrder: integer("sort_order").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Junction table for module-lesson relationships (many-to-many)
export const moduleLessons = pgTable("module_lessons", {
  id: uuid("id").primaryKey().defaultRandom(),
  moduleId: uuid("module_id").notNull().references(() => modules.id, { onDelete: 'cascade' }),
  lessonId: uuid("lesson_id").notNull().references(() => lessons.id, { onDelete: 'cascade' }),
  sortOrder: integer("sort_order").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Subscribers table (users who purchased products)
export const subscribers = pgTable("subscribers", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id", { length: 255 }).notNull(), // Clerk user ID
  productId: uuid("product_id").notNull().references(() => products.id, { onDelete: 'cascade' }),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  stripeCustomerId: varchar("stripe_customer_id", { length: 255 }),
  stripeSubscriptionId: varchar("stripe_subscription_id", { length: 255 }),
  status: subscriptionStatusEnum("status").notNull().default('active'),
  purchaseDate: timestamp("purchase_date").defaultNow().notNull(),
  expiryDate: timestamp("expiry_date"), // null for lifetime access
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Certificates table
export const certificates = pgTable("certificates", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  signerName: varchar("signer_name", { length: 255 }).notNull(),
  signerPosition: varchar("signer_position", { length: 255 }).notNull(),
  template: text("template"), // HTML template for certificate
  backgroundImage: text("background_image"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Junction table for certificate-course relationships (many-to-many)
export const certificateCourses = pgTable("certificate_courses", {
  id: uuid("id").primaryKey().defaultRandom(),
  certificateId: uuid("certificate_id").notNull().references(() => certificates.id, { onDelete: 'cascade' }),
  courseId: uuid("course_id").notNull().references(() => courses.id, { onDelete: 'cascade' }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User certificates (issued certificates)
export const userCertificates = pgTable("user_certificates", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id", { length: 255 }).notNull(), // Clerk user ID
  certificateId: uuid("certificate_id").notNull().references(() => certificates.id, { onDelete: 'cascade' }),
  courseId: uuid("course_id").notNull().references(() => courses.id, { onDelete: 'cascade' }),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  issuedAt: timestamp("issued_at").defaultNow().notNull(),
  certificateUrl: text("certificate_url"), // URL to generated certificate PDF
});

// Landing pages table
export const landingPages = pgTable("landing_pages", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  productId: uuid("product_id").notNull().references(() => products.id, { onDelete: 'cascade' }),
  title: varchar("title", { length: 255 }).notNull(),
  description: text("description"),
  slug: varchar("slug", { length: 255 }).notNull().unique(),
  content: jsonb("content"), // Page builder content
  isPublished: boolean("is_published").default(false).notNull(),
  customDomain: varchar("custom_domain", { length: 255 }),
  seoTitle: varchar("seo_title", { length: 255 }),
  seoDescription: text("seo_description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Coupons table
export const coupons = pgTable("coupons", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  code: varchar("code", { length: 50 }).notNull().unique(),
  description: text("description"),
  discountType: varchar("discount_type", { length: 20 }).notNull(), // 'percentage' or 'fixed'
  discountValue: decimal("discount_value", { precision: 10, scale: 2 }).notNull(),
  maxUses: integer("max_uses"), // null for unlimited
  usedCount: integer("used_count").default(0).notNull(),
  validFrom: timestamp("valid_from").defaultNow().notNull(),
  validUntil: timestamp("valid_until"),
  isActive: boolean("is_active").default(true).notNull(),
  stripeCouponId: varchar("stripe_coupon_id", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Junction table for coupon-product relationships (many-to-many)
export const couponProducts = pgTable("coupon_products", {
  id: uuid("id").primaryKey().defaultRandom(),
  couponId: uuid("coupon_id").notNull().references(() => coupons.id, { onDelete: 'cascade' }),
  productId: uuid("product_id").notNull().references(() => products.id, { onDelete: 'cascade' }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// User progress tracking
export const userProgress = pgTable("user_progress", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id", { length: 255 }).notNull(), // Clerk user ID
  lessonId: uuid("lesson_id").notNull().references(() => lessons.id, { onDelete: 'cascade' }),
  courseId: uuid("course_id").notNull().references(() => courses.id, { onDelete: 'cascade' }),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  isCompleted: boolean("is_completed").default(false).notNull(),
  completedAt: timestamp("completed_at"),
  watchTime: integer("watch_time").default(0), // in seconds
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Community feature tables
export const communityCategories = pgTable("community_categories", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  color: varchar("color", { length: 7 }), // hex color
  sortOrder: integer("sort_order").default(0),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const communityPosts = pgTable("community_posts", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: 'cascade' }),
  categoryId: uuid("category_id").notNull().references(() => communityCategories.id, { onDelete: 'cascade' }),
  authorId: varchar("author_id", { length: 255 }).notNull(), // Clerk user ID
  title: varchar("title", { length: 255 }).notNull(),
  content: text("content").notNull(),
  isPinned: boolean("is_pinned").default(false).notNull(),
  isLocked: boolean("is_locked").default(false).notNull(),
  viewCount: integer("view_count").default(0).notNull(),
  likeCount: integer("like_count").default(0).notNull(),
  replyCount: integer("reply_count").default(0).notNull(),
  lastReplyAt: timestamp("last_reply_at"),
  lastReplyBy: varchar("last_reply_by", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const communityReplies = pgTable("community_replies", {
  id: uuid("id").primaryKey().defaultRandom(),
  postId: uuid("post_id").notNull().references(() => communityPosts.id, { onDelete: 'cascade' }),
  authorId: varchar("author_id", { length: 255 }).notNull(), // Clerk user ID
  content: text("content").notNull(),
  parentReplyId: uuid("parent_reply_id").references(() => communityReplies.id, { onDelete: 'cascade' }), // for nested replies
  likeCount: integer("like_count").default(0).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const communityLikes = pgTable("community_likes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id", { length: 255 }).notNull(), // Clerk user ID
  postId: uuid("post_id").references(() => communityPosts.id, { onDelete: 'cascade' }),
  replyId: uuid("reply_id").references(() => communityReplies.id, { onDelete: 'cascade' }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Keep the old todo table for backward compatibility
export const todo = pgTable("todo", {
  id: integer("id").primaryKey(),
  text: text("text").notNull(),
  done: boolean("done").default(false).notNull(),
});

// Define relations
export const organizationsRelations = relations(organizations, ({ many }) => ({
  teams: many(teams),
  products: many(products),
  courses: many(courses),
  modules: many(modules),
  lessons: many(lessons),
  subscribers: many(subscribers),
  certificates: many(certificates),
  landingPages: many(landingPages),
  coupons: many(coupons),
  userProgress: many(userProgress),
  communityCategories: many(communityCategories),
  communityPosts: many(communityPosts),
}));

export const teamsRelations = relations(teams, ({ one }) => ({
  organization: one(organizations, {
    fields: [teams.organizationId],
    references: [organizations.id],
  }),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [products.organizationId],
    references: [organizations.id],
  }),
  productCourses: many(productCourses),
  subscribers: many(subscribers),
  landingPages: many(landingPages),
  couponProducts: many(couponProducts),
}));

export const coursesRelations = relations(courses, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [courses.organizationId],
    references: [organizations.id],
  }),
  productCourses: many(productCourses),
  courseModules: many(courseModules),
  certificateCourses: many(certificateCourses),
  userCertificates: many(userCertificates),
  userProgress: many(userProgress),
}));

export const modulesRelations = relations(modules, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [modules.organizationId],
    references: [organizations.id],
  }),
  courseModules: many(courseModules),
  moduleLessons: many(moduleLessons),
}));

export const lessonsRelations = relations(lessons, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [lessons.organizationId],
    references: [organizations.id],
  }),
  moduleLessons: many(moduleLessons),
  userProgress: many(userProgress),
}));

export const productCoursesRelations = relations(productCourses, ({ one }) => ({
  product: one(products, {
    fields: [productCourses.productId],
    references: [products.id],
  }),
  course: one(courses, {
    fields: [productCourses.courseId],
    references: [courses.id],
  }),
}));

export const courseModulesRelations = relations(courseModules, ({ one }) => ({
  course: one(courses, {
    fields: [courseModules.courseId],
    references: [courses.id],
  }),
  module: one(modules, {
    fields: [courseModules.moduleId],
    references: [modules.id],
  }),
}));

export const moduleLessonsRelations = relations(moduleLessons, ({ one }) => ({
  module: one(modules, {
    fields: [moduleLessons.moduleId],
    references: [modules.id],
  }),
  lesson: one(lessons, {
    fields: [moduleLessons.lessonId],
    references: [lessons.id],
  }),
}));

export const subscribersRelations = relations(subscribers, ({ one }) => ({
  product: one(products, {
    fields: [subscribers.productId],
    references: [products.id],
  }),
  organization: one(organizations, {
    fields: [subscribers.organizationId],
    references: [organizations.id],
  }),
}));

export const certificatesRelations = relations(certificates, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [certificates.organizationId],
    references: [organizations.id],
  }),
  certificateCourses: many(certificateCourses),
  userCertificates: many(userCertificates),
}));

export const certificateCoursesRelations = relations(certificateCourses, ({ one }) => ({
  certificate: one(certificates, {
    fields: [certificateCourses.certificateId],
    references: [certificates.id],
  }),
  course: one(courses, {
    fields: [certificateCourses.courseId],
    references: [courses.id],
  }),
}));

export const userCertificatesRelations = relations(userCertificates, ({ one }) => ({
  certificate: one(certificates, {
    fields: [userCertificates.certificateId],
    references: [certificates.id],
  }),
  course: one(courses, {
    fields: [userCertificates.courseId],
    references: [courses.id],
  }),
  organization: one(organizations, {
    fields: [userCertificates.organizationId],
    references: [organizations.id],
  }),
}));

export const landingPagesRelations = relations(landingPages, ({ one }) => ({
  organization: one(organizations, {
    fields: [landingPages.organizationId],
    references: [organizations.id],
  }),
  product: one(products, {
    fields: [landingPages.productId],
    references: [products.id],
  }),
}));

export const couponsRelations = relations(coupons, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [coupons.organizationId],
    references: [organizations.id],
  }),
  couponProducts: many(couponProducts),
}));

export const couponProductsRelations = relations(couponProducts, ({ one }) => ({
  coupon: one(coupons, {
    fields: [couponProducts.couponId],
    references: [coupons.id],
  }),
  product: one(products, {
    fields: [couponProducts.productId],
    references: [products.id],
  }),
}));

export const userProgressRelations = relations(userProgress, ({ one }) => ({
  lesson: one(lessons, {
    fields: [userProgress.lessonId],
    references: [lessons.id],
  }),
  course: one(courses, {
    fields: [userProgress.courseId],
    references: [courses.id],
  }),
  organization: one(organizations, {
    fields: [userProgress.organizationId],
    references: [organizations.id],
  }),
}));

export const communityCategoriesRelations = relations(communityCategories, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [communityCategories.organizationId],
    references: [organizations.id],
  }),
  posts: many(communityPosts),
}));

export const communityPostsRelations = relations(communityPosts, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [communityPosts.organizationId],
    references: [organizations.id],
  }),
  category: one(communityCategories, {
    fields: [communityPosts.categoryId],
    references: [communityCategories.id],
  }),
  replies: many(communityReplies),
  likes: many(communityLikes),
}));

export const communityRepliesRelations = relations(communityReplies, ({ one, many }) => ({
  post: one(communityPosts, {
    fields: [communityReplies.postId],
    references: [communityPosts.id],
  }),
  parentReply: one(communityReplies, {
    fields: [communityReplies.parentReplyId],
    references: [communityReplies.id],
  }),
  childReplies: many(communityReplies),
  likes: many(communityLikes),
}));

export const communityLikesRelations = relations(communityLikes, ({ one }) => ({
  post: one(communityPosts, {
    fields: [communityLikes.postId],
    references: [communityPosts.id],
  }),
  reply: one(communityReplies, {
    fields: [communityLikes.replyId],
    references: [communityReplies.id],
  }),
}));
