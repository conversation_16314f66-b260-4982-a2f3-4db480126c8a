{"InApp": {"navbar": {"home": "Home", "pricing": "Pricing", "support": "Support"}, "sidebar": {"publish": {"widgetPublish": {"title": "Publish", "publish": "Publish", "draft": "Draft", "status": "Status", "save": "Save", "saveDraft": "Save Draft"}, "widgetImages": {"title": "Images", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "cover": "Cover", "upload": "Upload"}}, "studio": {"sectionTitle": "Studio", "menuAI": {"aiCreator": "AI Creator"}}, "courses": {"sectionTitle": "Courses", "menuCourses": {"courses": "Courses", "newCourse": "New course", "allCourses": "All Courses", "categories": "Categorias"}, "menuModules": {"modules": "<PERSON><PERSON><PERSON>", "newModule": "New Module", "allModules": "All Modules"}, "menuLessons": {"lessons": "Lessons", "newLesson": "New Lesson", "allLessons": "All Lessons"}, "menuCertificates": {"certificates": "Certificates", "newCertificate": "New Certificate", "allCertificates": "All Certificates"}, "menuSubscriptions": {"subscriptions": "Subscriptions", "newSubscription": "New Subscription", "allSubscriptions": "All Subscriptions"}, "menuSubscribers": {"subscribers": "Subscribers", "newSubscriber": "New Subscriber", "allSubscribers": "All Subscribers", "rolesAndPermissions": "Roles & Permissions"}, "organizations": {"sectionTitle": "Organizations", "menuOrganizations": {"organizations": "Organizations", "newOrganization": "New Organization", "allOrganizations": "All Organizations"}}, "menuLandingPages": {"landingPages": "Landing Pages", "newLandingPage": "New Landing Page", "allLandingPages": "All Landing Pages"}}, "organizations": {"sectionTitle": "Organizations", "menuOrganizations": {"organizations": "Organizations", "newOrganization": "New Organization", "allOrganizations": "All Organizations"}}, "products": {"sectionTitle": "Products", "menuProducts": {"products": "Products", "newProduct": "New Product", "allProducts": "All Products"}, "menuCoupons": {"coupons": "Coupons", "newCoupon": "New Coupon", "allCoupons": "All Coupons"}, "menuRevenue": {"revenue": "Revenue"}}, "account": {"sectionTitle": "Account", "menuTeams": {"teams": "Teams", "newTeam": "New Equipe", "allTeams": "All Teams"}, "menuAccount": {"myAccount": "My Account", "menuProfile": "My Perfil", "payments": "Payments", "plans": "Plans", "menuLogout": "Logout"}}}, "tables": {"customer": "Customer", "sale": "Sale", "type": "Type", "status": "Status", "date": "Date", "actions": "Actions", "subTotal": "Subtotal", "total": "Total", "details": "Details", "name": "Name", "email": "Email", "phone": "Phone", "role": "role", "createdAt": "Created at", "updatedAt": "Updated at", "amount": "Amount", "price": "Price", "description": "Description", "category": "Category", "image": "Image", "refund": "Refund", "declined": "Declined", "approved": "Approved", "pending": "Pending", "paid": "Paid", "unpaid": "Unpaid", "incomplete": "Incomplete", "complete": "Complete", "draft": "Draft", "subscriptions": "Subscriptions", "subscription": "Subscription", "course": "Course", "module": "<PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON>", "lesson": "Lesson", "lessons": "Lessons", "student": "Student", "students": "Students", "organization": "Organization", "landingPage": "<PERSON>", "certificate": "Certificate", "permissions": "Permissions", "new": "New", "all": "All", "edit": "Edit", "delete": "Delete", "active": "Active", "inactive": "Inactive", "title": "Title"}, "pages": {"signInUp": {"sectionTitle": "Sign In", "sectionTitleSignUp": "Sign Up", "sectionTitleForgotPassword": "Forgot Password?", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "signIn": "Sign In", "signInHint": "Already have an account?", "signUp": "Sign Up", "signUpHint": "Don't have an account?"}, "dashboard": {"sectionTitle": "Dashboard", "fromLastMonth": "from last month", "sinceLastHour": "since last hour", "totalRevenue": "Total Revenue", "subscriptions": "Subscriptions", "sales": "Sales", "activeNow": "Active Now", "recentSales": "Recent Sales", "recentTransactionsFrom": "Recent transactions from your shop", "recentStudents": "Recent Students", "viewAll": "View All"}, "organizations": {"sectionTitle": "Organizations", "organization": "Organization", "subscribers": "Subscribers", "category": "Category", "newOrganization": "New Organization", "allOrganizations": "All Organizations", "courses": "Courses"}, "courses": {"sectionTitle": "Courses", "category": "Category", "modules": "<PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "lessons": "Lessons", "lesson": "Lesson", "students": "Students", "student": "Student", "subscribers": "Subscribers", "subscriber": "Subscriber", "duration": "Duration", "newCourse": "New Course", "allCourses": "All Courses", "newModule": "New Module", "allModules": "All Modules", "newLesson": "New Lesson", "allLessons": "All Lessons", "newCertificate": "New Certificate", "allCertificates": "All Certificates", "newStudent": "New Student", "allStudents": "All Students", "newLandingPage": "New Landing Page", "allLandingPages": "All Landing Pages", "myCourses": "My Courses"}, "modules": {"sectionTitle": "<PERSON><PERSON><PERSON>", "category": "Category", "status": "Status", "selectCourse": "Select Course", "selectStatus": "Select Status", "modules": "<PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "newModule": "New Module", "allModules": "All Modules", "filter": "Filter", "lessons": "Lessons", "course": "Course"}, "lessons": {"sectionTitle": "Lessons", "category": "Category", "status": "Status", "selectCourse": "Select Course", "selectStatus": "Select Status", "modules": "<PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "newLesson": "New Lesson", "newMultipleLessons": "Bulk Add Lessons (CSV)", "allLessons": "All Lessons", "filter": "Filter", "lesson": "Lesson", "lessons": "Lessons", "course": "Course", "description": "Description", "materials": "Materials"}, "certificates": {"sectionTitle": "Certificates", "category": "Category", "status": "Status", "selectCourse": "Select Course", "selectStatus": "Select Status", "newCertificate": "New Certificate", "allCertificates": "All Certificates", "filter": "Filter", "lesson": "Certificate", "lessons": "Certificates", "course": "Course"}, "landingPages": {"sectionTitle": "Landing Pages", "category": "Category", "status": "Status", "selectCourse": "Select Course", "selectStatus": "Select Status", "newLandingPage": "New Landing Page", "allLandingPages": "All Landing Pages", "filter": "Filter", "lesson": "<PERSON>", "lessons": "Landing Pages", "course": "Course"}, "new": {"sectionTitle": "New", "newCourse": "New Course", "newModule": "New Module", "newLesson": "New Lesson", "newCertificate": "New Certificate", "newStudent": "New Student", "newLandingPage": "New Landing Page", "newOrganization": "New Organization", "newSubscription": "New Subscription", "info": "Info", "content": "Content", "structure": "Structure", "typeToSearch": "Type to search for", "modules": "<PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "lessons": "Lessons", "lesson": "Lesson", "students": "Students", "student": "Student", "duration": "Duration", "suggestions": "Suggestions", "signature": "Signature", "preview": "Preview", "materials": "Materials", "styles": "Styles", "logo": "Logo", "defaultLogo": "<PERSON><PERSON><PERSON>", "negativeLogo": "Negative Logo", "defaultMobileLogo": "Default Mobile Logo", "negativeMobileLogo": "Negative Mobile Logo", "colors": "Colors", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "tertiaryColor": "Tertiary Color", "accentColor": "Accent Color", "backgroundColor": "Background Color", "color": "Color", "selectColorHint": "Select your primary, secondary, tertiary, background, and accent colors.", "selectLogoHint": "Select your Organization Logo. Negative Logos are logos that works in dark mode. The logo will show up on your Landing Pages and Certificates.", "pageBuilder": "Page Builder", "pageBuilderHint": "Use the Page Builder to create your Landing Page.", "open": "Open", "landingPage": "<PERSON>"}}, "builder": {"close": "Close", "save": "Save", "cancel": "Cancel", "confirm": "Confirmation", "edit": "Edit", "delete": "Delete", "search": "Search", "add": "Add", "addHint": "Click and drag to add", "copy": "Copy", "move": "Move", "moveTo": "Move to", "duplicate": "Duplicate", "selectAll": "Select all", "deselectAll": "Deselect all", "empty": "Empty", "template": "Template", "templates": "Templates", "loading": "Loading...", "block": "Block", "blocks": "Blocks", "components": "Components", "component": "Component", "page": "Page", "pages": "Pages", "format": "Format", "styles": "Styles", "settings": "Settings", "settingsHint": "Click to edit settings", "options": "Options", "optionsHint": "Click to edit options", "addBlock": "Add Block", "addBlockHint": "Click to add a block", "addPage": "Add Page", "addPageHint": "Click to add a page", "text": "Text", "image": "Image", "video": "Video", "embed": "Embed", "link": "Link", "embedHint": "Click to embed a link", "linkHint": "Click to edit the link", "imageHint": "Click to edit the image", "videoHint": "Click to edit the video", "componentHint": "Click to edit the component", "componentSettingsHint": "Click to edit the component settings", "alignRight": "Align Right", "alignCenter": "Align Center", "alignLeft": "Align Left", "alignJustify": "Justify", "alignNone": "None", "alignTop": "Align Top", "alignMiddle": "Align Middle", "alignBottom": "Align Bottom", "alignBaseline": "Align <PERSON>", "alignSub": "Sub", "alignSuper": "Super", "alignTopHint": "Click to align at the top", "alignMiddleHint": "Click to align in the middle", "alignBottomHint": "Click to align at the bottom", "alignSubHint": "Click to align on the baseline", "alignSuperHint": "Click to align on the superscript", "alignJustifyHint": "Click to justify", "alignLeftHint": "Click to align to the left", "alignCenterHint": "Click to align to the center", "alignRightHint": "Click to align to the right", "alignNoneHint": "Click to remove alignment", "alignBaselineHint": "Click to align on the baseline", "align": "Alignment", "alignHint": "Click to align", "color": "Color", "colorHint": "Click to edit the color"}, "forms": {"validation": {"minChar3": "must be at least 3 characters long"}, "title": "Title", "position": "Position", "positionHint": "Director, CEO, etc.", "name": "Name", "category": "Category", "description": "Description", "course": "Course", "courses": "Courses", "lesson": "Lesson", "lessons": "Lessons", "module": "<PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON>", "student": "Student", "students": "Students", "certificate": "Certificate", "certificates": "Certificates", "landingPage": "<PERSON>", "landingPages": "Landing Pages", "revenue": "Revenue", "total": "Total", "status": "Status", "itemsOrder": "Order", "itemsOrderHint": "(Click and drag to change the order)", "moduleBelongsToCourse": "Courses that this Module belongs to:", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "cover": "Cover", "publish": "Publish", "save": "Save", "draft": "Draft", "dropFiles": "Drop your files here or browse", "maxSize": "Maximum size", "createdAt": "Created at", "publishedAt": "Published at", "select": "Select", "courseStatus": "Course status:", "moduleStatus": "Module status:", "lessonStatus": "Lesson status:", "studentStatus": "Student status:", "certificateStatus": "Certificate status:", "add": "Add", "videoUrl": "Video Url", "addModule": "<PERSON><PERSON>", "addLesson": "<PERSON><PERSON>", "addStudent": "Add Student", "addCertificate": "Add Certificate", "type": "Type", "layoutType": "Layout Type", "signatureImage": "Signature Image", "organization": "Organization", "preview": "Preview", "team": "Team", "teams": "Teams", "product": "Product", "products": "Products", "subscription": "Subscription", "subscriptions": "Subscriptions"}}}