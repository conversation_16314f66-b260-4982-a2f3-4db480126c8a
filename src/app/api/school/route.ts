import { NextResponse } from 'next/server';
import { db } from '@/db';
import { todo } from '@/schema/todo';

export async function GET() {
  try {
    // Test database connection by fetching all todos
    const todos = await db.select().from(todo);

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: todos,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database connection error:', error);

    return NextResponse.json({
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text) {
      return NextResponse.json({
        success: false,
        message: 'Text is required'
      }, { status: 400 });
    }

    // Insert a new todo
    const newTodo = await db.insert(todo).values({
      text,
      done: false
    }).returning();

    return NextResponse.json({
      success: true,
      message: 'Todo created successfully',
      data: newTodo[0]
    });
  } catch (error) {
    console.error('Database insert error:', error);

    return NextResponse.json({
      success: false,
      message: 'Failed to create todo',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}