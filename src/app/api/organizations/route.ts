import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getOrganizations, getUserOrganizations } from '@/lib/actions/organizations';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeTeams = searchParams.get('includeTeams') === 'true';

    let organizations;
    
    if (includeTeams) {
      organizations = await getUserOrganizations(userId);
    } else {
      organizations = await getOrganizations(userId);
    }

    return NextResponse.json({
      success: true,
      data: organizations,
    });
  } catch (error) {
    console.error('Error fetching organizations:', error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Failed to fetch organizations',
    }, { status: 500 });
  }
}
