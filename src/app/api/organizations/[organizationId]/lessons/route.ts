import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getLessons } from '@/lib/actions/lessons';

export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const lessons = await getLessons(params.organizationId);

    return NextResponse.json({
      success: true,
      data: lessons,
    });
  } catch (error) {
    console.error('Error fetching lessons:', error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Failed to fetch lessons',
    }, { status: 500 });
  }
}
