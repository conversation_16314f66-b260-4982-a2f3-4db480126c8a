import { SignIn } from "@clerk/nextjs";
import { getTranslations, setRequestLocale } from "next-intl/server";
import Image from "next/image";
import Link from "next/link";

type Props = {
  params: Promise<{ locale: string }>;
};

const SignInPage = async ({ params }: Props) => {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations("InApp.pages.signInUp");
  return (
    <div className="w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          <SignIn
            appearance={{
              elements: {
                footer: "hidden",
                cardBox: "shadow-none border border-gray-200",
                formButtonPrimary:
                  "bg-slate-900 hover:bg-slate-600 !shadow-none appearance-none",
              },
            }}
          />

          <p className="text-sm text-slate-500 text-center">
            {t("signUpHint")}{" "}
            <Link
              className="font-bold text-slate-900 underline"
              href="/sign-up"
            >
              {t("signUp")}
            </Link>{" "}
          </p>
        </div>
      </div>
      <div className="hidden bg-muted lg:block">
        <Image
          src="/placeholder.svg"
          alt="Image"
          width="1920"
          height="1080"
          className="h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
    </div>
  );
};

export default SignInPage;
