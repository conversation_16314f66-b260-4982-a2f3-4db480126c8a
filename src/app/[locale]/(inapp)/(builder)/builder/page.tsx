"use client";
import { Editor, Frame, Element } from "@craftjs/core";
import { Button } from "@/components/builder/ui/Button";
import { Text } from "@/components/builder/ui/Text";
import { Container } from "@/components/builder/ui/Container";
import Sidebar from "@/components/builder/panel/Sidebar";
import { Settings } from "@/components/builder/panel/Settings";

const Builder = () => {
  return (
    <Editor
      resolver={{ Button, Text, Container }}
      indicator={{ success: "#94a3b8", error: "#e34850" }}
    >
      <div className="w-full flex h-screen ">
        <div className="w-[280px]">
          <Sidebar />
        </div>
        <Frame>
          <div className="w-[calc(100% - 560px)] min-h-screen">
            <Element is={Container} canvas>
              <Text text="This is a test" fontSize={24} textAlign="left" />
              <Button>Button</Button>
            </Element>
          </div>
        </Frame>
        <div className="w-[220px]">
          <Settings />
        </div>
      </div>
    </Editor>
  );
};
export default Builder;
