"use client";
import Sidebar from "@/components/inapp/Sidebar";
import Navbar from "@/components/inapp/Navbar";
import { useEffect, useState, MouseEvent } from "react";
import { ChevronLeft, ChevronRight, Menu } from "lucide-react";

export type Props = {
  sidebar: null | string;
  main: null | string;
  children: React.ReactNode;
};

export default function InAppLayout({ sidebar, main, children }: Props) {
  const [collapsedSidebar, setCollapsedSidebar] = useState(false);
  const [customStyles, setCustomStyles] = useState({
    sidebar: "w-[56px] 2xl:w-[220px] hover:w-[220px]",
    main: "w-[calc(100%-56px)] 2xl:w-[calc(100%-220px)]",
  });

  return (
    <div className="flex flex-row min-h-screen h-full h-screen w-full overflow-hidden bg-white z-20">
      <div className="2xl:relative flex flex-col h-screen w-full overflow-hidden">
        <header className="w-full z-20">
          <Navbar isCollapsed={collapsedSidebar} />
        </header>
        <div className="flex w-full">
          <main
            className={`w-full flex flex-col fixed top-0 right-0 relative min-h-[94.2vh] h-screen 2xl:h-full 2xl:overflow-hidden bg-slate-50/50`}
          >
            <section className="h-full w-full overflow-hidden transition-all z-10 relative">
              <div className="relative h-full w-full overflow-x-hidden overflow-y-scroll">
                <div className="flex flex-col 2xl:flex-row h-full w-full overflow-hidden">
                  <div className="w-full xh-full overflow-y-auto scrollbar-hide">
                    {children}
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>
  );
}
