"use client";
import Sidebar from "@/components/inapp/Sidebar";
import Navbar from "@/components/inapp/Navbar";
import { useEffect, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export type Props = {
  sidebar: null | string;
  main: null | string;
  children: React.ReactNode;
};

export default function InAppLayout({ sidebar, main, children }: Props) {
  const [collapsedSidebar, setCollapsedSidebar] = useState(false);
  const [customStyles, setCustomStyles] = useState({
    sidebar: "w-[64px] 2xl:w-[220px] hover:w-[220px]",
    main: "w-[calc(100%-64px)] 2xl:w-[calc(100%-220px)]",
  });

  const styles = () => {
    if (collapsedSidebar) {
      setCustomStyles({
        sidebar: "w-[64px] 2xl:w-[64px] hover:w-[220px] 2xl:hover:w-[64px]",
        main: "w-[calc(100%-64px)] 2xl:w-[calc(100%-64px)]",
      });
    } else {
      setCustomStyles({
        sidebar: "w-[64px] 2xl:w-[220px] hover:w-[220px] 2xl:hover:w-[220px]",
        main: "w-[calc(100%-64px)] 2xl:w-[calc(100%-220px)]",
      });
    }
  };

  useEffect(() => {
    styles();
  }, [collapsedSidebar]);

  return (
    <div className="flex flex-row min-h-screen h-screen w-full overflow-hidden bg-white z-20">
      <div className="2xl:relative flex flex-col h-screen w-full overflow-hidden">
        <header className="w-full z-20">
          <Navbar isCollapsed={collapsedSidebar} />
        </header>
        <div className="flex w-full">
          <aside
            className={`${customStyles.sidebar} relative group border-r border-gray-200 bg-white hidden lg:flex z-20 flex h-full flex-shrink-0 flex-grow-0 flex-col transition-all`}
          >
            <button
              className="w-6 h-6 border border-gray-200 rounded-md hidden absolute top-2 -right-[12px] 2xl:flex z-20 items-center justify-center bg-white hover:bg-gray-100"
              onClick={() => setCollapsedSidebar(!collapsedSidebar)}
            >
              {collapsedSidebar ? (
                <ChevronRight size={16} />
              ) : (
                <ChevronLeft size={16} />
              )}
            </button>
            <Sidebar isCollapsed={collapsedSidebar} />
          </aside>
          <main
            className={`${customStyles.main} flex flex-col fixed top-0 right-0 relative min-h-[94.2vh] h-screen   bg-slate-50/50`}
          >
            <section className="h-full w-full overflow-hidden transition-all z-10 relative">
              <div className="relative h-full w-full overflow-x-hidden overflow-y-scroll">
                <div className="flex flex-col 2xl:flex-row h-full w-full overflow-hidden">
                  <div className="w-full xh-full overflow-y-auto scrollbar-hide">
                    {children}
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>
  );
}
