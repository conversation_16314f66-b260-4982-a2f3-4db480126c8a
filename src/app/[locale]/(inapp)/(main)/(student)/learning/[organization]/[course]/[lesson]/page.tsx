import ModulesList from "@/components/inapp/CourseModules";
import CourseTabs from "@/components/inapp/CourseTabs";
import VideoPlayer from "@/components/inapp/VideoPlayer";
import { useTranslations } from "next-intl";

export type Props = {
  params: {
    organization: string;
    course: string;
  };
};

const Course = ({ params }: Props) => {
  const t = useTranslations("InApp.pages.courses");
  return (
    <div className="w-full flex flex-col p-8">
      <div className="w-full flex flex-col gap-0">
        <h1 className="font-semibold text-4xl">Lesson Title</h1>
        <h3 className="text-md text-slate-500 mb-2">Course Name</h3>
      </div>
      <div className="grid grid-cols-[1fr_300px] gap-8  mb-20">
        <div className="flex flex-col gap-4">
          <VideoPlayer url="https://www.youtube.com/watch?v=FtA-th_Uyg4" />
          <CourseTabs />
        </div>
        <ModulesList />
      </div>
    </div>
  );
};
export default Course;
