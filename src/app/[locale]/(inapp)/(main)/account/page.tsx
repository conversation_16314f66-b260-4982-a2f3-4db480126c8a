/**
 * v0 by Vercel.
 * @see https://v0.dev/t/Af63LMlfsB3
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON>bs<PERSON>ontent } from "@/components/ui/tabs";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { UserProfile } from "@clerk/nextjs";

export default function Component() {
  return (
    <div className="w-full xl:max-w-5xl mx-auto p-8 md:py-12">
      <Card>
        <CardContent className="grid gap-4">
          {/* <UserProfile
            path="/account"
            routing="path"
            appearance={{
              elements: {
                rootBox: "border-none shadow-none bg-transparent",
                cardBox: "border-none shadow-none bg-transparent",
                navbar: "bg-transparent bg-gradient-to-r from-white to-white ",
                scrollBox: "bg-transparent rounded-none",
              },
            }}
          /> */}
        </CardContent>
        <CardFooter>
          <Button>Save Changes</Button>
        </CardFooter>
      </Card>

      {/* <Tabs defaultValue="billing" className="grid gap-6 ">
        <TabsList className="flex border-none bg-transparent">
          <TabsTrigger
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
            value="profile"
          >
            Profile
          </TabsTrigger>
          <TabsTrigger
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
            value="billing"
          >
            Billing Info
          </TabsTrigger>
          <TabsTrigger
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
            value="history"
          >
            Purchase History
          </TabsTrigger>

          <TabsTrigger
            className="border-b-4 data-[state=active]:border-slate-800 data-[state=active]:shadow-none rounded-none shadow-none"
            value="password"
          >
            Reset Password
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="grid gap-6">
          <Card>
            <CardContent className="grid gap-4">
              
            </CardContent>
            <CardFooter>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="billing" className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Method</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="card-number">Card Number</Label>
                <Input id="card-number" defaultValue="**** **** **** 1234" />
              </div>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="expiry">Expiry</Label>
                  <Input id="expiry" defaultValue="12/24" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="cvc">CVC</Label>
                  <Input id="cvc" defaultValue="123" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Billing Address</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="address-1">Address 1</Label>
                <Input id="address-1" defaultValue="123 Main St" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="address-2">Address 2</Label>
                <Input id="address-2" defaultValue="Apt 456" />
              </div>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="city">City</Label>
                  <Input id="city" defaultValue="San Francisco" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="state">State</Label>
                  <Input id="state" defaultValue="CA" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="zip">Zip</Label>
                  <Input id="zip" defaultValue="94103" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Subscription</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Pro Plan</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Billed monthly
                  </div>
                </div>
                <div className="text-2xl font-bold">$19</div>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>Next payment due</div>
                <div className="font-medium">June 1, 2024</div>
              </div>
              <div className="flex items-center justify-between">
                <div>Subscription expires</div>
                <div className="font-medium">May 31, 2025</div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline">Cancel Subscription</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="history" className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Purchase History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Item</TableHead>
                    <TableHead>Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>June 18, 2024</TableCell>
                    <TableCell>Pro Plan Subscription</TableCell>
                    <TableCell>$19.00</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>May 1, 2024</TableCell>
                    <TableCell>WhimsiMug</TableCell>
                    <TableCell>$99.00</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>March 15, 2024</TableCell>
                    <TableCell>Acme Toolkit</TableCell>
                    <TableCell>$49.99</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password" className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Reset Password</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="current-password">Current Password</Label>
                <Input id="current-password" type="password" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="new-password">New Password</Label>
                <Input id="new-password" type="password" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="confirm-password">Confirm Password</Label>
                <Input id="confirm-password" type="password" />
              </div>
            </CardContent>
            <CardFooter>
              <Button>Update Password</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs> */}
    </div>
  );
}
