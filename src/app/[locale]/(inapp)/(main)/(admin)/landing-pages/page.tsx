import AddNewButton from "@/components/inapp/AddNewButton";
import { LandingPageCard } from "@/components/inapp/LandingPageCard";
import { useTranslations } from "next-intl";

const LandingPages = ({ params }: { params: { organization: string } }) => {
  const t = useTranslations("InApp.pages.landingPages");

  const landing = {
    id: "999888777666",
    title: "Landing 1",
    category: "Design",
    product: "Codesigners Yearly",
    organization: "Codesigners",
    slug: "course-1",
    image: "/placeholder.svg",
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
      <header className="flex flex-row items-center">
        <h1 className="font-semibold text-4xl mr-4">{t("sectionTitle")}</h1>
        <AddNewButton label={t("newLandingPage")} href={`/landingpages/add`} />
      </header>
      <div className="grid gap-4 md:grid-cols-3 md:gap-8  2xl:grid-cols-5">
        <LandingPageCard item={landing} />
      </div>
    </div>
  );
};

export default LandingPages;
