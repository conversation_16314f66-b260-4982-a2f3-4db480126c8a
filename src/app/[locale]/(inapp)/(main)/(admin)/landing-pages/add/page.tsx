"use client";
import { useState } from "react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Reorder, useDragControls } from "framer-motion";
import { GripVertical, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@radix-ui/react-label";
import ImageUploader from "@/components/inapp/ImageUploader";
import Link from "next/link";

const NewCourse = () => {
  const t = useTranslations("InApp.pages.new");
  const tf = useTranslations("InApp.forms");
  const tv = useTranslations("InApp.forms.validation");

  const formSchema = z.object({
    title: z.string().min(3, { message: `${tf("title")} ${tv("minChar3")}` }),
    product: z
      .string()
      .min(3, { message: `${tf("product")} ${tv("minChar3")}` }),
    course: z.string().min(3, { message: `${tf("course")} ${tv("minChar3")}` }),
    description: z
      .string()
      .min(3, { message: `${tf("description")} ${tv("minChar3")}` }),
    modules: z
      .string()
      .min(3, { message: `${tf("description")} ${tv("minChar3")}` }),
  });

  //Setting Zod Resolver to validate our form based on the formSchema everytime the form changes
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const handleSubmit = () => {};
  return (
    <section className="flex flex-col w-full">
      <header className="flex flex-row items-center mb-8">
        <h1 className="font-semibold text-4xl mr-4">{t("newOrganization")}</h1>
      </header>
      <div className="grid grid-3 w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="grid grid-cols-3 gap-4 w-full"
          >
            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1">
              {t("info")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-3">
                    <FormLabel className="font-semibold">
                      {tf("title")}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={tf("title")} type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <div className="col-span-3">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="course"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("course")}
                        </FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("course")}</SelectLabel>
                                <SelectItem value="course">Course 1</SelectItem>
                                <SelectItem value="course">Course 2</SelectItem>
                                <SelectItem value="course">Course 3</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="product"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("product")}
                        </FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("product")}</SelectLabel>
                                <SelectItem value="product">
                                  Product 1
                                </SelectItem>
                                <SelectItem value="product">
                                  Product 2
                                </SelectItem>
                                <SelectItem value="product">
                                  Product 3
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            </div>

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("landingPage")}
            </h2>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>{t("pageBuilder")}</CardTitle>
                <CardDescription>{t("pageBuilderHint")}</CardDescription>
              </CardHeader>
              <CardContent className="flex align-center justify-center pt-8 pb-20">
                <Button asChild>
                  <Link href="/builder">
                    {" "}
                    {`${t("open")} ${t("pageBuilder")}`}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default NewCourse;
