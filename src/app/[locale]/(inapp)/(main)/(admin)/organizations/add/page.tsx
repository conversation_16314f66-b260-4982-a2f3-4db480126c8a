"use client";
import { useState } from "react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Reorder, useDragControls } from "framer-motion";
import { GripVertical, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@radix-ui/react-label";
import ImageUploader from "@/components/inapp/ImageUploader";

const NewCourse = () => {
  const t = useTranslations("InApp.pages.new");
  const tf = useTranslations("InApp.forms");
  const tv = useTranslations("InApp.forms.validations");

  const formSchema = z.object({
    name: z.string().min(3, { message: `${tf("name")}${tv("minChars3")}` }),
    description: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
    modules: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
  });

  //Setting Zod Resolver to validate our form based on the formSchema everytime the form changes
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const handleSubmit = () => {};
  return (
    <section className="flex flex-col w-full">
      <header className="flex flex-row items-center mb-8">
        <h1 className="font-semibold text-4xl mr-4">{t("newOrganization")}</h1>
      </header>
      <div className="grid grid-3 w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="grid grid-cols-3 gap-4 w-full"
          >
            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1">
              {t("info")}
            </h2>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-2">
                    <FormLabel className="font-semibold">
                      {tf("name")}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={tf("name")} type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel className="font-semibold">
                      {tf("team")}
                    </FormLabel>
                    <FormControl>
                      <Select {...field}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={tf("select")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>{tf("team")}</SelectLabel>
                            <SelectItem value="design">Team 1</SelectItem>
                            <SelectItem value="development">Team 2</SelectItem>
                            <SelectItem value="marketing">Marketing</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("styles")}
            </h2>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>{t("logo")}</CardTitle>
                <CardDescription>{t("selectLogoHint")}</CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-4 gap-4">
                <ImageUploader label={t("defaultLogo")} />
                <ImageUploader label={t("negativeLogo")} />
                <ImageUploader label={t("defaultMobileLogo")} />
                <ImageUploader label={t("negativeMobileLogo")} />
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>{t("colors")}</CardTitle>
                <CardDescription>{t("selectColorHint")}</CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-3 gap-4">
                <div className="flex items-end gap-2">
                  <div className="w-10 h-10 rounded-md bg-[#0070f3]" />
                  <div className="flex-1">
                    <Label htmlFor="color1">{t("primaryColor")}</Label>
                    <Input id="color1" type="text" defaultValue="#0070f3" />
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <div className="w-10 h-10 rounded-md bg-[#ff4d4f]" />
                  <div className="flex-1">
                    <Label htmlFor="color2">{t("secondaryColor")}</Label>
                    <Input id="color2" type="text" defaultValue="#ff4d4f" />
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <div className="w-10 h-10 rounded-md bg-[#52c41a]" />
                  <div className="flex-1">
                    <Label htmlFor="color3">{t("tertiaryColor")}</Label>
                    <Input id="color3" type="text" defaultValue="#52c41a" />
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  <div className="w-10 h-10 rounded-md bg-[#52c41a]" />
                  <div className="flex-1">
                    <Label htmlFor="color3">{t("backgroundColor")}</Label>
                    <Input id="color3" type="text" defaultValue="#52c41a" />
                  </div>
                </div>

                <div className="flex items-end gap-2">
                  <div className="w-10 h-10 rounded-md bg-[#52c41a]" />
                  <div className="flex-1">
                    <Label htmlFor="color3">{t("accentColor")}</Label>
                    <Input id="color3" type="text" defaultValue="#52c41a" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default NewCourse;
