import SidebarPublish from "@/components/inapp/SidebarPublish";

export default function InAppLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex min-h-screen flex-row">
      <main className=" flex flex-col w-[calc(100%-320px)] p-8 mb-20">
        {children}
      </main>
      <aside className="w-[320px] border-r border-gray-200 bg-white h-screen fixed top-14 right-0 z-10">
        <SidebarPublish type="lesson" />
      </aside>
    </div>
  );
}
