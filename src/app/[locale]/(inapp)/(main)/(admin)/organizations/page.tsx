import AddNewButton from "@/components/inapp/AddNewButton";
import { OrganizationCard } from "@/components/inapp/OrganizationCard";
import { useTranslations } from "next-intl";

const Organizations = () => {
  const t = useTranslations("InApp.pages.organizations");

  const organizations = {
    title: "Organization 1",
    category: "Design",
    courses: { count: 8 },
    subscribers: { count: 1200 },
    id: "999888777666",
    slug: "organization-1",
    image: "/placeholder.svg",
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
      <header className="flex flex-row items-center">
        <h1 className="font-semibold text-4xl mr-4">{t("sectionTitle")}</h1>
        <AddNewButton
          label={t("newOrganization")}
          href={`/organizations/add`}
        />
      </header>
      <div className="grid gap-4 md:grid-cols-3 md:gap-8  2xl:grid-cols-5">
        <OrganizationCard item={organizations} />
      </div>
    </div>
  );
};

export default Organizations;
