"use client";
import { useState } from "react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import Tiptap from "@/components/inapp/TipTap";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Reorder, useDragControls } from "framer-motion";
import { ArrowUpRight, CirclePlus, GripVertical, Trash, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

const NewLesson = () => {
  const t = useTranslations("InApp.pages.new");
  const tf = useTranslations("InApp.forms");
  const tv = useTranslations("InApp.forms.validations");
  const tb = useTranslations("InApp.tables");

  const [items, setItems] = useState([
    "item 1",
    "item 2",
    "item 3",
    "item 4",
    "item 5",
    "item 6",
    "item 7",
    "item 8",
    "item 9",
    "item 10",
  ]);
  const controls = useDragControls();

  const handleReorder = (e: any) => {
    console.log(e);
    setItems(e);
  };

  const formSchema = z.object({
    title: z.string().min(3, { message: `${tf("title")}${tv("minChars3")}` }),
    description: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
    videoUrl: z
      .string()
      .min(3, { message: `${tf("videoUrl")}${tv("minChars3")}` }),
    modules: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
  });

  //Setting Zod Resolver to validate our form based on the formSchema everytime the form changes
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const handleSubmit = () => {};
  return (
    <section className="flex flex-col w-full">
      <header className="flex flex-row items-center mb-8">
        <h1 className="font-semibold text-4xl mr-4">{t("newLesson")}</h1>
      </header>
      <div className="grid grid-3 w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="grid grid-cols-3 gap-4 w-full"
          >
            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1">
              {t("info")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-2">
                    <FormLabel className="font-semibold">
                      {tf("title")}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={tf("title")} type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel className="font-semibold">
                      {tf("type")}
                    </FormLabel>
                    <FormControl>
                      <Select {...field}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={tf("select")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>{tf("type")}</SelectLabel>
                            <SelectItem value="design">Text</SelectItem>
                            <SelectItem value="development">Video</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("content")}
            </h2>

            <FormField
              control={form.control}
              name="videoUrl"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-3">
                    <FormLabel className="font-semibold">
                      {tf("videoUrl")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={tf("videoUrl")}
                        type="text"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-3">
                    <FormLabel className="font-semibold">
                      {tf("description")}
                    </FormLabel>
                    <FormControl>
                      <Tiptap
                        description={field.name}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("structure")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("course")}
                        </FormLabel>
                        <FormControl>
                          <Select {...field}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("type")}</SelectLabel>
                                <SelectItem value="design">Text</SelectItem>
                                <SelectItem value="development">
                                  Video
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("module")}
                        </FormLabel>
                        <FormControl>
                          <Select {...field}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("type")}</SelectLabel>
                                <SelectItem value="design">Text</SelectItem>
                                <SelectItem value="development">
                                  Video
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <Button className="mt-8">
              <CirclePlus size={16} className="mr-2" />
              {tf("add")}
            </Button>
            <div className="col-span-3">
              <Card className="mt-4">
                <CardContent className="p-6">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{tb("title")}</TableHead>
                        <TableHead>{tb("course")}</TableHead>
                        <TableHead>{tb("module")}</TableHead>
                        <TableHead className="text-right">
                          {tb("actions")}
                        </TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      <TableRow>
                        <TableCell>
                          O que você vai aprender neste curso
                        </TableCell>
                        <TableCell>Course 1</TableCell>
                        <TableCell>Introducao</TableCell>
                        <TableCell className="text-right">
                          <div className="w-full flex flex-row items-center justify-end">
                            <Button
                              variant="outline"
                              size="sm"
                              className="p-2 text-[9px] h-6 text-destructive"
                            >
                              Delete
                              <Trash size={10} className="ml-1" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default NewLesson;
