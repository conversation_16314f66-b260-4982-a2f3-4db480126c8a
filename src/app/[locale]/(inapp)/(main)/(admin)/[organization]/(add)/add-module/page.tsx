"use client";
import { useState } from "react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import Tiptap from "@/components/inapp/TipTap";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Reorder, useDragControls } from "framer-motion";
import { GripVertical, X } from "lucide-react";
import { Button } from "@/components/ui/button";

// Draggable Lesson Item Component
const DraggableLessonItem = ({
  item,
  index,
  onRemove
}: {
  item: { id: string; name: string };
  index: number;
  onRemove: () => void;
}) => {
  const controls = useDragControls();

  return (
    <Reorder.Item
      value={item}
      dragControls={controls}
      dragListener={false}
      className="flex flex-row justify-between items-center p-2 border border-gray-200 rounded-md bg-white"
    >
      <div className="flex flex-row gap-2 items-center">
        <GripVertical
          size={12}
          onPointerDown={(e) => controls.start(e)}
          className="w-4 h-4 hover:cursor-grab active:cursor-grabbing"
        />
        {item.name} - index: {index}
      </div>
      <Button
        variant="ghost"
        size="sm"
        className="end h-6 w-6 px-0"
        onClick={onRemove}
        type="button"
      >
        <X size={12} />
      </Button>
    </Reorder.Item>
  );
};

const NewModule = () => {
  const t = useTranslations("InApp.pages.new");
  const tf = useTranslations("InApp.forms");
  const tv = useTranslations("InApp.forms.validations");
  const [items, setItems] = useState([
    { id: "1", name: "Lesson 1" },
    { id: "2", name: "Lesson 2" },
    { id: "3", name: "Lesson 3" },
    { id: "4", name: "Lesson 4" },
    { id: "5", name: "Lesson 5" },
  ]);

  const handleReorder = (newItems: typeof items) => {
    console.log('Reordered items:', newItems);
    setItems(newItems);
  };

  const formSchema = z.object({
    title: z.string().min(3, { message: `${tf("title")}${tv("minChars3")}` }),
    description: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
    modules: z
      .string()
      .min(3, { message: `${tf("description")}${tv("minChars3")}` }),
  });

  //Setting Zod Resolver to validate our form based on the formSchema everytime the form changes
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const handleSubmit = () => {};
  return (
    <section className="flex flex-col w-full">
      <header className="flex flex-row items-center mb-8">
        <h1 className="font-semibold text-4xl mr-4">{t("newModule")}</h1>
      </header>
      <div className="grid grid-3 w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="grid grid-cols-3 gap-4 w-full"
          >
            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1">
              {t("info")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-3">
                    <FormLabel className="font-semibold">
                      {tf("title")}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={tf("title")} type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("content")}
            </h2>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-3">
                    <FormLabel className="font-semibold">
                      {tf("description")}
                    </FormLabel>
                    <FormControl>
                      <Tiptap
                        description={field.name}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("structure")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("moduleBelongsToCourse")}
                        </FormLabel>
                        <FormControl>
                          <Select {...field}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("type")}</SelectLabel>
                                <SelectItem value="module 0">
                                  Module 0
                                </SelectItem>
                                <SelectItem value="module 1">
                                  Module 1
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <div className="col-span-2 flex flex-col">
              <FormLabel className="font-semibold mb-4">
                {tf("courses")}
              </FormLabel>
              <Reorder.Group
                values={items}
                onReorder={(e) => handleReorder(e)}
                className="flex flex-col gap-2"
              >
                {items.map((item, index) => {
                  return (
                    <Reorder.Item
                      value={item}
                      key={item}
                      dragControls={controls}
                      dragListener={true}
                    >
                      <div className="flex flex-row justify-between items-center p-2 border border-gray-200 rounded-md bg-white">
                        <div className="flex flex-row gap-2 items-center">
                          <GripVertical
                            size={12}
                            onPointerDown={(e) => controls.start(e)}
                            className="w-4 h-4 hover:cursor-grab active:cursor-grabbing"
                          />
                          {item} - index: {index}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="end h-6 w-6 px-0"
                        >
                          <X size={12} />
                        </Button>
                      </div>
                    </Reorder.Item>
                  );
                })}
              </Reorder.Group>
            </div>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default NewModule;
