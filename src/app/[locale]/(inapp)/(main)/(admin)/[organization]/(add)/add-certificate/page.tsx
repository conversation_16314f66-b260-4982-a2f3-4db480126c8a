"use client";
import { useState } from "react";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import Tiptap from "@/components/inapp/TipTap";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useTranslations } from "next-intl";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Reorder, useDragControls } from "framer-motion";
import { GripVertical, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import ImageUploader from "@/components/inapp/ImageUploader";

const NewModule = () => {
  const t = useTranslations("InApp.pages.new");
  const tf = useTranslations("InApp.forms");
  const tv = useTranslations("InApp.forms.validation");
  const [items, setItems] = useState([
    "item 1",
    "item 2",
    "item 3",
    "item 4",
    "item 5",
    "item 6",
    "item 7",
    "item 8",
    "item 9",
    "item 10",
  ]);
  const controls = useDragControls();

  const handleReorder = (e: any) => {
    console.log(e);
    setItems(e);
  };

  const formSchema = z.object({
    title: z.string().min(3, { message: `${tf("title")} ${tv("minChar3")}` }),
    name: z.string().min(3, { message: `${tf("title")} ${tv("minChar3")}` }),
    organization: z
      .string()
      .min(3, { message: `${tf("title")} ${tv("minChar3")}` }),
    position: z
      .string()
      .min(3, { message: `${tf("title")} ${tv("minChar3")}` }),
    description: z
      .string()
      .min(3, { message: `${tf("description")} ${tv("minChar3")}` }),
    modules: z
      .string()
      .min(3, { message: `${tf("description")} ${tv("minChar3")}` }),
  });

  //Setting Zod Resolver to validate our form based on the formSchema everytime the form changes
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const handleSubmit = () => {};
  return (
    <section className="flex flex-col w-full">
      <header className="flex flex-row items-center mb-8">
        <h1 className="font-semibold text-4xl mr-4">{t("newCertificate")}</h1>
      </header>
      <div className="grid grid-3 w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="grid grid-cols-3 gap-4 w-full"
          >
            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1">
              {t("info")}
            </h2>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => {
                return (
                  <FormItem className="col-span-2">
                    <FormLabel className="font-semibold">
                      {tf("title")}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={tf("title")} type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="organization"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <FormItem>
                        <FormLabel className="font-semibold">
                          {tf("organization")}
                        </FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={tf("select")} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>{tf("organization")}</SelectLabel>
                                <SelectItem value="module 0">
                                  School 1
                                </SelectItem>
                                <SelectItem value="module 1">
                                  School 2
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("signature")}
            </h2>

            <div className="col-span-3">
              <div className="grid grid-cols-2 gap-4 w-full  mb-8">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel className="font-semibold col-span-2">
                          {tf("name")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={tf("name")}
                            type="text"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel className="font-semibold col-span-2">
                          {tf("position")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={tf("positionHint")}
                            type="text"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
              <ImageUploader label={tf("signatureImage")} />
            </div>

            <h2 className="uppercase text-sm text-gray-500 col-span-3 border-l-4 border-gray-200 pl-1 mt-4">
              {t("preview")}
            </h2>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default NewModule;
