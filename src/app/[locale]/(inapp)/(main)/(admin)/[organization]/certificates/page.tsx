import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowUpRight, Eye, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CirclePlus } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import AddNewButton from "@/components/inapp/AddNewButton";

const Certificates = ({ params }: { params: { organization: string } }) => {
  const t = useTranslations("InApp.pages.certificates");
  const tb = useTranslations("InApp.tables");

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
      <header className="flex flex-row items-center">
        <h1 className="font-semibold text-4xl mr-4">{t("sectionTitle")}</h1>
        <AddNewButton
          label={t("newCertificate")}
          href={`/${
            params && params.organization ? params.organization : ""
          }/add-certificate`}
        />
      </header>
      <section>
        <div className="grid gap-6 sm:grid-cols-6">
          <div className="grid gap-3">
            <Label htmlFor="course">{t("course")}</Label>
            <Select>
              <SelectTrigger id="course" aria-label={t("selectCourse")}>
                <SelectValue placeholder={t("selectCourse")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="clothing">Design</SelectItem>
                <SelectItem value="electronics">Development</SelectItem>
                <SelectItem value="accessories">Marketing</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-3">
            <Label htmlFor="status">{t("status")}</Label>
            <Select>
              <SelectTrigger id="status" aria-label={t("selectStatus")}>
                <SelectValue placeholder={t("selectStatus")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="t-shirts">Draft</SelectItem>
                <SelectItem value="hoodies">Published</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-3 mt-6">
            <Button className="w-[100px]">{t("filter")}</Button>
          </div>
        </div>
      </section>
      <section>
        <Card>
          <CardContent className="p-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{tb("course")}</TableHead>
                  <TableHead>{tb("status")}</TableHead>
                  <TableHead className="text-right">{tb("actions")}</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                <TableRow>
                  <TableCell>Course 1</TableCell>
                  <TableCell>
                    <Badge className="text-xs" variant="outline">
                      draft
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="w-full flex flex-row items-center justify-end">
                      <Link
                        href="/[locale]/(inapp)/[organization]/modules/[module]"
                        as="/en/(inapp)/demo/courses/course-1"
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-2 text-[9px] h-6 mr-2"
                        >
                          Preview
                          <Eye size={10} className="ml-1" />
                        </Button>
                      </Link>
                      <Link
                        href="/[locale]/(inapp)/[organization]/modules/[module]"
                        as="/en/(inapp)/demo/courses/course-1"
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className="p-2 text-[9px] h-6 mr-2"
                        >
                          Edit
                          <ArrowUpRight size={10} className="ml-1" />
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        className="p-2 text-[9px] h-6 text-destructive"
                      >
                        Delete
                        <Trash size={10} className="ml-1" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};

export default Certificates;
