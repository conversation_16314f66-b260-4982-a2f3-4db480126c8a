import AddNewButton from "@/components/inapp/AddNewButton";
import { CourseCard } from "@/components/inapp/CourseCard";
import { useTranslations } from "next-intl";

const Courses = ({ params }: { params: { organization: string } }) => {
  const t = useTranslations("InApp.pages.courses");

  const course = {
    title: "Course 1",
    category: "Design",
    modules: ["Module 1", "Module 2"],
    lessons: ["Lesson 1", "Lesson 2"],
    duration: "4:30",
    students: { count: 1200 },
    id: "999888777666",
    slug: "course-1",
    image: "/placeholder.svg",
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
      <header className="flex flex-row items-center">
        <h1 className="font-semibold text-4xl mr-4">{t("sectionTitle")}</h1>
        <AddNewButton
          label={t("newCourse")}
          href={`/${
            params && params.organization ? params.organization : ""
          }/add-course`}
        />
      </header>
      <div className="grid gap-4 md:grid-cols-3 md:gap-8  2xl:grid-cols-5">
        <CourseCard item={course} />
      </div>
    </div>
  );
};

export default Courses;
