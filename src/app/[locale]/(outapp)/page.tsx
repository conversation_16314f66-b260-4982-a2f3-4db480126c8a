import { But<PERSON> } from "@/components/ui/button";
import { setRequestLocale } from "next-intl/server";

type Props = {
  params: Promise<{ locale: string }>;
};

export default async function Home({ params }: Props) {
  const { locale } = await params;
  setRequestLocale(locale);

  return (
    <main className="flex w-full h-screen justify-center align-center">
      <Button>Landing Page</Button>
    </main>
  );
}
