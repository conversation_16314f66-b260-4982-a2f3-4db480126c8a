# Database Schema Documentation

## Overview

This document describes the database schema for the course platform, which supports multiple organizations (schools), courses, modules, lessons, products, and community features.

## Core Entities

### Organizations
- **Purpose**: Main entity representing schools/communities
- **Key Features**: 
  - Integrated with Clerk organizations
  - Plan-based limits (Basic: 1, Professional: 3, Pro: 10, Business: 30)
  - Unique slugs for custom URLs

### Teams
- **Purpose**: Organization members with roles
- **Roles**: 
  - `admin`: Organization owner, full permissions
  - `editor`: Can create/edit content (teachers/instructors)

### Products
- **Purpose**: Purchasable items containing courses
- **Payment Types**: 
  - `one_time`: Single payment
  - `monthly`: Monthly subscription
  - `annually`: Annual subscription
- **Access Control**: Optional expiration (in months) or lifetime access

### Courses
- **Purpose**: Educational content containers
- **Features**: Categorization, publishing status, custom slugs

### Modules
- **Purpose**: Course subdivisions for better organization
- **Relationships**: Many-to-many with courses (reusable modules)

### Lessons
- **Purpose**: Individual learning units
- **Types**: `video`, `text`, `quiz`
- **Features**: Rich content, video URLs, duration tracking

## Advanced Features

### Certificates
- **Purpose**: Completion certificates for courses
- **Features**: Custom templates, signer information, automatic issuance

### Landing Pages
- **Purpose**: Marketing pages for products
- **Features**: Page builder content (JSON), SEO optimization, custom domains

### Coupons
- **Purpose**: Discount codes for products
- **Types**: `percentage` or `fixed` amount discounts
- **Features**: Usage limits, expiration dates, product-specific

### Community
- **Purpose**: Forum-like features for each organization
- **Components**:
  - Categories: Organized discussion topics
  - Posts: User-generated content with likes/views
  - Replies: Threaded discussions with nested replies
  - Likes: Engagement tracking

### Progress Tracking
- **Purpose**: Track user progress through lessons
- **Features**: Completion status, watch time, course progress

## Relationships

### Many-to-Many Relationships
- **Products ↔ Courses**: Products can contain multiple courses
- **Courses ↔ Modules**: Courses can have multiple modules, modules can be reused
- **Modules ↔ Lessons**: Modules can have multiple lessons, lessons can be reused
- **Certificates ↔ Courses**: Certificates can be awarded for multiple courses
- **Coupons ↔ Products**: Coupons can apply to multiple products

### One-to-Many Relationships
- **Organizations → Everything**: All content belongs to an organization
- **Users → Progress**: Track individual user progress
- **Users → Certificates**: Issued certificates per user

## Access Control

### Organization Access Levels
1. **Owner** (`admin`): Full control, can manage team, billing
2. **Team Member** (`editor`): Can create/edit content
3. **Subscriber**: Can access purchased content and community

### Permission Matrix
| Action | Owner | Editor | Subscriber |
|--------|-------|--------|------------|
| Manage Organization | ✅ | ❌ | ❌ |
| Manage Team | ✅ | ❌ | ❌ |
| Create/Edit Content | ✅ | ✅ | ❌ |
| Access Purchased Content | ✅ | ✅ | ✅ |
| Community Participation | ✅ | ✅ | ✅ |

## API Structure

### Server Actions
Located in `src/lib/actions/`:
- `organizations.ts`: Organization CRUD
- `teams.ts`: Team management
- `courses.ts`: Course management
- `modules.ts`: Module management
- `lessons.ts`: Lesson management
- `products.ts`: Product management
- `certificates.ts`: Certificate management
- `landing-pages.ts`: Landing page management
- `coupons.ts`: Coupon management
- `community.ts`: Community features

### API Routes
Located in `src/app/api/`:
- `/api/organizations`: Organization endpoints
- `/api/organizations/[id]/courses`: Course endpoints
- `/api/organizations/[id]/modules`: Module endpoints
- `/api/organizations/[id]/lessons`: Lesson endpoints

## Database Migrations

### Running Migrations
```bash
# Generate migration files
npx drizzle-kit generate

# Apply migrations to database
npx drizzle-kit push

# View database in Drizzle Studio
npx drizzle-kit studio
```

### Environment Variables
```env
DATABASE_URL=postgresql://localhost:5432/t_chr_db
```

## Plan Limits

### Organization Limits by Plan
- **Basic**: 1 organization
- **Professional**: 3 organizations  
- **Pro**: 10 organizations
- **Business**: 30 organizations

These limits are enforced in the `createOrganization` server action and checked via Clerk's organization features.

## Community Features

### Inspired by Circle.so and Skool.com
- **Categories**: Organized discussion areas
- **Posts**: Rich content with engagement metrics
- **Replies**: Threaded discussions
- **Likes**: Simple engagement system
- **Moderation**: Pin/lock posts, admin controls

### Access Control
- Only subscribers and team members can access community
- All members can post and reply
- Admins/editors can moderate content
